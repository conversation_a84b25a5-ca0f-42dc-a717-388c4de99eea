import React, { useEffect, useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,CFormSelect,
  CFormInput,
  CFormLabel,
  CButton
} from '@coreui/react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';

const AddSlowPayment = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const isEdit = id !== null;

  const navigate = useNavigate()

  const { data: cash, loading, error: cashError } = useFetch(isEdit ? 'other/singleSlow/' + id : '');
  
  const [customer, setCustomer] = useState('');
  const [phone, setPhone] = useState('');
  const [PhoneID, setPhoneID] = useState('');
  const [PhoneName, setPhoneName] = useState('');
 const [amount, setAmount] = useState('');
 const [startAmount, setStartAmount] = useState('');

  useEffect(() => {
    if (cash) {
      setCustomer(cash.CustomerName);
      setPhone(cash.PhoneNumber);
      setPhoneID(cash.PhoneID);
      setPhoneName(cash.NeededPhone);
      setAmount(cash.NeededAmount);
      setStartAmount(cash.AmountPaid);
    }
  }, [cash]);

  const [error, setError] = useState('');
  const [submitted, setSubmitted] = useState(false);

  const handleAddCash = async (e) => {
    e.preventDefault();

     // Prevent double submission
     if (submitted) return;

     setSubmitted(true);

    setError('');

    if (!customer || !phone || !amount) {
      setError('Please fill all required fields.');
      return;
    }

    const token = localStorage.getItem('joh_token');

    if (!token) {
      throw new Error('No token found');
    }

    const alowData = {
      customer,
      phone,
      PhoneID,
      PhoneName,
      startAmount,
      amount
    };


    try {
      let response;

      // If we're editing, use PUT request, otherwise use POST
      if (isEdit) {
        response = await axios.put(`${api_path}/api/other/updateSlow/${id}`, alowData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      } else {
        response = await axios.post(`${api_path}/api/other/addSlow`, alowData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      }

      if (response.status === 200) {
        toast.success(`${isEdit ? 'Slow Payment Updated' : 'Slow Payment Added'} successfully`);
        setCustomer('');
        setPhone('');
        setAmount('');
        setPhoneID('');
        navigate('/view-slow-payments');
      }

      if (response.status === 400) {
        toast.error(response.message);
        setError(response.message )
        setCustomer('');
        setPhone('');
        setAmount('');
        setPhoneID('');
        navigate('/view-slow-payments');
      }
    } catch (error) {
      console.error(error);

      if (error.response) {
        setError(error.response.data.message || 'An error occurred. Please try again.');
      } else if (error.request) {
        setError('Network error. Please check your connection.');
      } else {
        setError('An unexpected error occurred.');
      }

      toast.error(error.message || 'Failed to add cash');
    }
    finally {
        setSubmitted(false); 
      }
  };

  if (cashError) return <div>Error loading cash data: {cashError.message}</div>;
  if (loading) return <div>Loading...</div>;

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit' : 'Add'} Slow Payment</strong>
          </CCardHeader>
          <CCardBody>
            <form action="" method="post" onSubmit={handleAddCash}>
            <CRow className="mb-3">
              {error && <p className="text-danger">{error}</p>}
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Jina la Mteja:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  value={customer}
                  onChange={(e) => setCustomer(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Namba ya Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="tel"
                  id="phone"
                  required
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Jina la Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  required
                  value={PhoneName}
                  onChange={(e) => setPhoneName(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="bei" className="col-sm-2 col-form-label">
                Bei ya Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="bei"
                  required
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                />
              </div>
            </CRow>
            {!isEdit && (

                <CRow className="mb-3">
                  <CFormLabel htmlFor="start" className="col-sm-2 col-form-label">
                    Kiasi alicholipia:
                  </CFormLabel>
                  <div className="col-sm-10">
                    <CFormInput
                      type="number"
                      id="start"
                      value={startAmount}
                      onChange={(e) => setStartAmount(e.target.value)}
                    />
                  </div>
                </CRow>
            )}



                <CRow className="w-25 mx-auto">
                  <CButton color="primary" type="submit" disabled={submitted} >
                    Submit
                  </CButton>
                </CRow>
              
            


        </form>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default AddSlowPayment;
