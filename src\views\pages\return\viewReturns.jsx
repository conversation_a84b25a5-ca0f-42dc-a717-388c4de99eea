import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CFormInput,
  CPagination,
  CPaginationItem
} from '@coreui/react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';
import { FaFilePdf } from "react-icons/fa6";
import { IoAddCircleSharp } from "react-icons/io5"

import useFetch from '../../../hooks/useFetch'; 
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'
import { DatePicker , Tooltip , Drawer} from 'antd';
const { RangePicker } = DatePicker;
import { jsPDF } from "jspdf";
import { useMediaQuery } from 'react-responsive';
import dayjs from 'dayjs';


const ViewReturns = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';
  const isMobile = useMediaQuery({ query: '(max-width:767px)' })

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10000; 
  const [selectedDate, setSelectedDate] = useState();

  const { data: allReturns , loading , error} = useFetch('other/allReturns');
  
  const initialCashRecords = Array.isArray(allReturns)
  ? allReturns.map(aReturn => ({
    id: aReturn.ReturnID,
    customer: aReturn.CustomerName,
    phone: aReturn.PhoneNumber, 
    RPpName: aReturn.RPPhoneName,
    RPimei1: aReturn.RPIMEI1,
    RPimei2: aReturn.RPIMEI2,
    TPpName: aReturn.TPPhoneName,
    TPimei1: aReturn.TPIMEI1,
    TPimei2: aReturn.TPIMEI2,
    agent: aReturn.UserName,
    date: aReturn.created_at
  }))
  : [];
  

  const filteredStaffs = initialCashRecords.filter(aReturn => {
    // Text search filter
    const matchesSearch = !searchTerm ||
      (aReturn.customer && aReturn.customer.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (aReturn.phone && aReturn.phone.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (aReturn.RPpName && aReturn.RPpName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (aReturn.RPimei1 && aReturn.RPimei1.includes(searchTerm)) ||
      (aReturn.TPimei1 && aReturn.TPimei1.includes(searchTerm));

    // Date filter
    const matchesDate = !selectedDate ||
      (aReturn.date && dayjs(aReturn.date).format('YYYY-MM-DD') === selectedDate);

    return matchesSearch && matchesDate;
  });

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDeleteReturn =async (returnID) => {
    const response = await axios.post(`${api_path}/api/other/deleteReturn/${returnID}`);

    if(response.status == 200){
      toast.success("Return Deleted successfully");
      window.location.reload();
    }
  }

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading users: {error.message}</div>;
 
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
    });
  };

  const exportToPDF = () => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 10;
    const cellPadding = 2;
    const xStart = margin;
    const title = "Return Records";
  
    // Define headers and data
    const headers = ["Date", "Customer", "Iliyorudishwa", "Aliyochukua", "Agent"];
    const dataForPdf = currentStaffs.map(cash => [
      cash.date ? formatDate(cash.date) : '',
      cash.customer || '',
      (cash.RPpName || 'N/A') + ' - ' + (cash.RPimei1 ? cash.RPimei1.slice(-9) : 'N/A'),
      (cash.TPpName || 'N/A') + ' - ' + (cash.TPimei1 ? cash.TPimei1.slice(-9) : 'N/A'),
      cash.agent || ''
    ]);

    // Calculate column widths
    const columnWidths = headers.map((header, index) => {
    const headerWidth = doc.getStringUnitWidth(header) * doc.internal.getFontSize();
    const maxDataWidth = Math.max(...dataForPdf.map(row => {
      const cellValue = row[index] || ''; // Handle undefined/null values
      return doc.getStringUnitWidth(String(cellValue)) * doc.internal.getFontSize();
    }));
    return Math.max(headerWidth, maxDataWidth) + 2 * cellPadding;
  });
  
    // Adjust columns if they exceed page width
    const totalTableWidth = columnWidths.reduce((sum, width) => sum + width, 0);
    if (totalTableWidth > (pageWidth - 2 * margin)) {
      const scaleFactor = (pageWidth - 2 * margin) / totalTableWidth;
      columnWidths.forEach((_, i) => columnWidths[i] *= scaleFactor);
    }
  
    // Draw title and headers on current page
    const drawTitleAndHeaders = () => {
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(title, pageWidth / 2, 20, { align: "center" });
  
      doc.setFontSize(9);
      headers.forEach((header, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0) + cellPadding;
        doc.text(header, xPosition, 35); // Header text at Y=35
      });
  
      headers.forEach((_, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0);
        doc.rect(xPosition, 30, columnWidths[i], 10); // Header background
      });
    };
  
    // Initial page setup
    let currentPage = doc;
    let currentY = 40; // Start Y for data rows
    const cellHeight = 8;
    const maxY = pageHeight - 20; // Bottom margin
    let rowsOnCurrentPage = 0;
  
    // Draw initial headers
    drawTitleAndHeaders();
  
    for (let rowIndex = 0; rowIndex < dataForPdf.length; rowIndex++) {
      const row = dataForPdf[rowIndex];
  
      // Check if we need a new page
      if (currentY + (rowsOnCurrentPage + 1) * cellHeight > maxY) {
        currentPage.addPage();
        currentPage = currentPage;
        currentY = 40;
        rowsOnCurrentPage = 0;
        drawTitleAndHeaders();
      }
  
      // Draw row cells
      row.forEach((col, colIndex) => {
        const xPosition = xStart + columnWidths.slice(0, colIndex).reduce((sum, w) => sum + w, 0);
        const yPosition = currentY + (rowsOnCurrentPage * cellHeight);
  
        // Draw cell border
        doc.rect(xPosition, yPosition, columnWidths[colIndex], cellHeight);
  
        // Set font style and size
        doc.setFont("helvetica", "normal");
        let fontSize = 8;
        const availableWidth = columnWidths[colIndex] - 2 * cellPadding;
        let textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        
        // Adjust font size if needed
        while (textWidth > availableWidth && fontSize > 6) {
          fontSize -= 0.5;
          textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        }
        doc.setFontSize(fontSize);
  
        // Draw cell content
        doc.text(col.toString(), xPosition + cellPadding, yPosition + cellHeight - 2);
      });
  
      rowsOnCurrentPage++;
    }
  
    doc.save("return_records.pdf");
  };
  
  const onDateChange = (date, dateString) => {
    setSelectedDate(dateString);
  };

  return (
    <>
    {/* Modern Header Section */}
    <div className="modern-search-bar">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="mb-2">Return Records</h2>
          <p className="text-gray-600 mb-0">Manage and track all return transactions</p>
        </div>
        <div className="d-flex gap-3 align-items-center">
          {isAdmin && (
            <Tooltip title="Export To PDF">
              <button className="btn-modern btn-success-modern" onClick={exportToPDF}>
                <FaFilePdf /> Export PDF
              </button>
            </Tooltip>
          )}
          <Link to="/add-return">
            <Tooltip title="Add New Record">
              <button className="btn-modern btn-primary-modern">
                <IoAddCircleSharp /> Add Return
              </button>
            </Tooltip>
          </Link>
        </div>
      </div>

      {/* Enhanced Filter Controls */}
      <div className="filter-controls">
        <div className="d-flex gap-3 align-items-center flex-wrap">
          <div className="flex-grow-1" style={{ minWidth: '300px' }}>
            <CFormInput
              type="text"
              placeholder="🔍 Search by customer, phone, IMEI, or device name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="modern-form-input"
              style={{
                padding: '12px 16px',
                fontSize: '14px',
                border: '2px solid var(--gray-200)',
                borderRadius: '8px'
              }}
            />
          </div>
          <div className="d-flex gap-2 align-items-center">
            <DatePicker
              placeholder="📅 Filter by date"
              onChange={onDateChange}
              value={selectedDate ? dayjs(selectedDate) : null}
              allowClear
              style={{
                padding: '8px 12px',
                borderRadius: '8px'
              }}
            />
            {selectedDate && (
              <button
                className="btn-modern btn-secondary-modern"
                onClick={() => setSelectedDate(null)}
                title="Clear date filter"
              >
                Clear
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
    {/* Modern Table Section */}
    <div className="modern-card">
      <div className="modern-card-header">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h4 className="mb-1">Return Records</h4>
            <p className="text-gray-500 mb-0" style={{ fontSize: '14px' }}>
              Showing {filteredStaffs.length} of {initialCashRecords.length} records
              {selectedDate && ` • Filtered by date: ${selectedDate}`}
            </p>
          </div>
          <div className="d-flex gap-2 align-items-center">
            <span className="badge" style={{
              background: 'var(--primary-100)',
              color: 'var(--primary-700)',
              padding: '4px 12px',
              borderRadius: '20px',
              fontSize: '12px',
              fontWeight: '500'
            }}>
              {filteredStaffs.length} results
            </span>
          </div>
        </div>
      </div>
      <div className="modern-card-body" style={{ padding: '0' }}>
        <div className="table-responsive">
          <table className="modern-table w-100">
            <thead>
              <tr>
                <th style={{ width: '60px' }}>#</th>
                <th>Customer</th>
                <th>Returned Device</th>
                <th>Received Device</th>
                <th className="mobile-hidden">Date</th>
                {isAdmin && (
                  <>
                    <th className="mobile-hidden">Agent</th>
                    <th style={{ width: '120px' }}>Actions</th>
                  </>
                )}
              </tr>
            </thead>
            <tbody>
              {currentStaffs.length === 0 ? (
                <tr>
                  <td colSpan={isAdmin ? 7 : 5} className="text-center py-8">
                    <div style={{ padding: '40px 20px' }}>
                      <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
                      <h5 className="text-gray-600 mb-2">No records found</h5>
                      <p className="text-gray-500">
                        {searchTerm || selectedDate
                          ? 'Try adjusting your search or date filter'
                          : 'No return records available yet'}
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                currentStaffs.map((aReturn, index) => (
                  <tr key={aReturn.id}>
                    <td>
                      <span className="font-medium text-gray-700">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </span>
                    </td>
                    <td>
                      <div>
                        <div className="font-medium text-gray-900">
                          {aReturn.customer || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {aReturn.phone || 'No phone'}
                        </div>
                      </div>
                    </td>
                    <td>
                      <div className="d-flex align-items-center gap-2">
                        <div style={{
                          width: '8px',
                          height: '8px',
                          borderRadius: '50%',
                          background: 'var(--warning-500)'
                        }}></div>
                        <span className="font-medium">
                          {aReturn.RPpName || 'N/A'}
                        </span>
                      </div>
                      {aReturn.RPimei1 && (
                        <div className="text-xs text-gray-500 mt-1">
                          IMEI: {aReturn.RPimei1.slice(-9)}
                        </div>
                      )}
                    </td>
                    <td>
                      <div className="d-flex align-items-center gap-2">
                        <div style={{
                          width: '8px',
                          height: '8px',
                          borderRadius: '50%',
                          background: 'var(--success-500)'
                        }}></div>
                        <span className="font-medium">
                          {aReturn.TPpName || 'N/A'}
                        </span>
                      </div>
                      {aReturn.TPimei1 && (
                        <div className="text-xs text-gray-500 mt-1">
                          IMEI: {aReturn.TPimei1.slice(-9)}
                        </div>
                      )}
                    </td>
                    <td className="mobile-hidden">
                      <span className="text-sm text-gray-600">
                        {aReturn.date ? formatDate(aReturn.date) : 'N/A'}
                      </span>
                    </td>
                    {isAdmin && (
                      <>
                        <td className="mobile-hidden">
                          <span className="text-sm font-medium text-gray-700">
                            {aReturn.agent || 'N/A'}
                          </span>
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <Link to={'/single-record?type=return&id=' + aReturn.id}>
                              <Tooltip title="View Details">
                                <button className="btn-modern" style={{
                                  padding: '6px 8px',
                                  background: 'var(--gray-100)',
                                  border: '1px solid var(--gray-300)',
                                  borderRadius: '6px',
                                  color: 'var(--gray-600)'
                                }}>
                                  <CIcon icon={icon.cilChevronDoubleRight} size="sm" />
                                </button>
                              </Tooltip>
                            </Link>
                            <Link to={'/add-return?id=' + aReturn.id}>
                              <Tooltip title="Edit Record">
                                <button className="btn-modern" style={{
                                  padding: '6px 8px',
                                  background: 'var(--primary-100)',
                                  border: '1px solid var(--primary-300)',
                                  borderRadius: '6px',
                                  color: 'var(--primary-600)'
                                }}>
                                  <CIcon icon={icon.cilPenAlt} size="sm" />
                                </button>
                              </Tooltip>
                            </Link>
                            <Tooltip title="Delete Record">
                              <button
                                className="btn-modern"
                                style={{
                                  padding: '6px 8px',
                                  background: 'var(--error-100)',
                                  border: '1px solid var(--error-300)',
                                  borderRadius: '6px',
                                  color: 'var(--error-600)'
                                }}
                                onClick={() => {
                                  const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                                  if (isConfirmed) {
                                    handleDeleteReturn(aReturn.id);
                                  }
                                }}
                              >
                                <CIcon icon={icon.cilTrash} size="sm" />
                              </button>
                            </Tooltip>
                          </div>
                        </td>
                      </>
                    )}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Modern Pagination */}
        {totalPages > 1 && (
          <div className="modern-pagination">
            <button
              className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              ← Previous
            </button>

            {Array.from({ length: Math.min(totalPages, 5) }, (_, index) => {
              let pageNumber;
              if (totalPages <= 5) {
                pageNumber = index + 1;
              } else if (currentPage <= 3) {
                pageNumber = index + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNumber = totalPages - 4 + index;
              } else {
                pageNumber = currentPage - 2 + index;
              }

              return (
                <button
                  key={pageNumber}
                  className={`page-item ${pageNumber === currentPage ? 'active' : ''}`}
                  onClick={() => handlePageChange(pageNumber)}
                >
                  {pageNumber}
                </button>
              );
            })}

            <button
              className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next →
            </button>
          </div>
        )}
      </div>
    </div>
    </>
  );
};

export default ViewReturns;
