import React, { useEffect, useState } from 'react';
import {
    CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow
} from '@coreui/react';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';
import useFetch from '../../../hooks/useFetch';

import { Select, Tooltip } from 'antd';
import { toast } from 'react-toastify';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { Link } from 'react-router-dom';
import { useAuth } from '../../../hooks/AuthContext';


const Report = () => {
    const { role } = useAuth();
    const isAdmin = role === 'admin';

  const [selectedPhones, setSelectedPhones] = useState([]);
  const [freezedPhones, setFreezedPhones] = useState([]);
  const { data: phones } = useFetch('other/allPhone');
  const { data: myPhones } = useFetch('other/userAllPhones');

  const structuredPhones = phones?.map(phone => ({
    value: phone.PhoneID,  
    label: phone.PhoneName + ' - ' + phone.IMEI1.slice(-9),
  }));

  useEffect(() => {
    if (Array.isArray(myPhones) && myPhones.length > 0) {
      const formattedPhones = myPhones.map((phone) => ({
        value: phone.PhoneID,
        phone: {
          value: phone.PhoneID,
          label: `${phone.PhoneName} - ${phone.IMEI1.slice(-9)}`
        }
      }));
      setFreezedPhones(formattedPhones);
    }
  }, [myPhones]);
  

const handleSelectChange = (value, label) => {
    setSelectedPhones((prev) => {
        const exists = prev.some((phone) => phone.value === value);
        if (exists) {
            toast.error('Phone already selected!');
            return prev;
        }
        return [...prev, { value: value, phone: label }];
    });
};

const handleAdd = async (e) => {
    e.preventDefault();

    if (selectedPhones.length === 0) {
        toast.error('Please select at least one phone!');
        return;
    }

    const token = localStorage.getItem('joh_token');

    if (!token) {
      throw new Error('No token found');
    }
  
    const reportData = {
        phones: selectedPhones.map(phone => phone.value)
    };

    try {
        const response = await axios.post(`${api_path}/api/other/report`, reportData, {
            headers: {
                Authorization: `Bearer ${token}`
            }
        });

        if (response.status === 201 || response.status === 200) {
            window.location.reload();
            toast.success(`Report successfully`);
        }else{
            toast.error('Error submitting report!');
        }
        
    } catch (error) {
        toast.error('Error submitting report!');
        console.error('Error submitting report:', error);
    }
    
};


  
return (
    <CRow>
        <CCol xs={12}>
            {isAdmin && (

                <div className="d-flex justify-content-end">
                <Link to="/all-report" className="mx-3">
                <Tooltip title="View All Report">
                    <CButton color="primary" className="mb-4">
                        <CIcon icon={icon.cilArrowCircleRight} className="me-2" />
                        View All Report
                    </CButton>
                    </Tooltip>
                </Link>

                </div>
            )}


            <CCard className="mb-4">
                <CCardHeader>
                    <strong>Report</strong>
                </CCardHeader>
                <CCardBody>
                    <form action="" method="post" onSubmit={handleAdd}>
                        
                        {/* Select Input area */}
                        <Select
                            showSearch
                            className='w-100'
                            placeholder="Select a phone"
                            filterOption={(input, option) => {
                            var _a;
                            return (
                                    (_a = option === null || option === void 0 ? void 0 : option.label) !== null &&
                                    _a !== void 0
                                    ? _a
                                    : ''
                            )
                                    .toLowerCase()
                                    .includes(input.toLowerCase());
                            }}
                            options={structuredPhones}
                            onChange={handleSelectChange}
                    />

                    <CRow className="w-100 mt-3">
                            <div className="d-flex justify-content-between align-items-center">

                                    <div className="">
                                            <b>{selectedPhones.length}</b> phones selected
                                    </div>

                                    <div className="">

                                            {/* Submit button */}
                                            <CButton type="submit" color="primary" className="px-4" onClick={handleAdd}>
                                                    <CIcon icon={icon.cilSave} className="me-2" />
                                                    Submit
                                            </CButton>
                                    </div>
                            </div>
                    </CRow>
                    </form>
                </CCardBody>
            </CCard>

            <CCard className="mb-4">
                    <CCardHeader>
                            <strong>Selected Phones</strong>
                    </CCardHeader>
                    <CCardBody >
                        <div className="overflow-auto " style={{ maxHeight: '380px' }}>
                        {(selectedPhones.length > 0 ? selectedPhones : freezedPhones).length > 0 ? (
                            (selectedPhones.length > 0 ? selectedPhones : freezedPhones).map((phone,i) => (                                <>
                                <div className="d-flex justify-content-between align-items-center" key={phone.value}>
                                    <div className="">
                                            <span key={phone.value} className="me-2">
                                                  {++i}{'. '}{phone.phone.label}
                                            </span>
                                    </div>
                                    <div className="">
                                        {!freezedPhones.length > 0 && (

                                            <CButton color="danger" className="px-2 text-white" onClick={() => setSelectedPhones(selectedPhones.filter(p => p.value !== phone.value))}>
                                                <CIcon icon={icon.cilTrash} className="me-2" />
                                                Remove
                                            </CButton>
                                        )}
                                    </div>
                                </div>
                                            <hr />
                                </>
                             ))
                            ) 
                                    :
                            (
                                    <div className="text-center">
                                    <CIcon icon={icon.cilWarning} className="text-warning" style={{ fontSize: '5rem' }} />
                                    <h4 className="mt-3">No phone available</h4>
                                    <p>Please select a phone(s) to report.</p>
                                    </div>
                            )}
                        </div>
                    </CCardBody> 
            </CCard>   
        </CCol>

    </CRow>
);
};

export default Report;
