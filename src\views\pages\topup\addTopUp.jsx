import React, { useEffect, useState } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CFormInput,
  CFormLabel,
  CButton,  
} from '@coreui/react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';
import { UploadOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import { Select } from 'antd';


const AddTopUp = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const isEdit = id !== null;

  const navigate = useNavigate()
  
  const { data: allPhone , loading : phoneLoading } = useFetch('other/allPhone');

  
  const { data: topup, loading, error: cashError } = useFetch(isEdit ? 'other/singleTopup/' + id : '');
  
  const [customer, setCustomer] = useState('');
  const [phone, setPhone] = useState('');
  const [CPphoneName, setCPPhoneName] = useState('');
  const [CPimei1, setCPImei1] = useState('');
  const [CPimei2, setCPImei2] = useState('');
  const [PhoneID, setPhoneID] = useState('');
  const [NIDA, setNIDA] = useState('');
  const [cost, setCost] = useState('');
  const [closerUser, setCloserUser] = useState('');
  const [image, setImage] = useState('');

  const structuredPhones = allPhone?.map(phone => ({
    value: phone.PhoneID,  
    label: phone.PhoneName + ' - ' + phone.IMEI1.slice(-9),
  }));
  
  useEffect(() => {
    if (topup) {
      setCustomer(topup.CustomerName);
      setPhone(topup.PhoneNumber);
      setCPPhoneName(topup.CustomerPhone);
      setCPImei1(topup.CPIMEI1);
      setCPImei2(topup.CPIMEI2);
      setPhoneID(topup.PhoneID)
      setNIDA(topup.CustomerNIDA)
      setCost(topup.TopUpCost)
      setCloserUser(topup.CloserUserPhone)
      
    }
  }, [topup]);

  const [error, setError] = useState('');

  const props = {
    action: `${api_path}/api/other/addTopup`,  
    listType: 'picture',
    beforeUpload: (file) => {
      setImage(file);  
      return false;    
    },
    onRemove: () => {
      setImage(null);  
    }
  };

  const handleAdd = async (e) => {
    e.preventDefault();
  
    setError('');  // Clear any previous errors
  
    if (!customer || !phone || !CPphoneName || !CPimei1 || !cost || !closerUser || !image) {
      setError('Please fill all required fields, including image upload.');
      return;
    }
  
    const token = localStorage.getItem('joh_token');
    if (!token) {
      throw new Error('No token found');
    }
  
    const topupData = {
      customer,
      phone,
      CPphoneName,
      CPimei1,
      CPimei2,
      PhoneID,
      NIDA,
      closerUser,
      cost,
      image
    };
  
    try {
      let response;
      
      // Create FormData object to send the image with other data
      const formData = new FormData();
      formData.append('customer', customer);
      formData.append('phone', phone);
      formData.append('CPphoneName', CPphoneName);
      formData.append('CPimei1', CPimei1);
      formData.append('CPimei2', CPimei2);
      formData.append('PhoneID', PhoneID);
      formData.append('NIDA', NIDA);
      formData.append('closerUser', closerUser);
      formData.append('cost', cost);
      formData.append('image', image); 
  
      // If we're editing, use PUT request, otherwise use POST
      if (isEdit) {
        response = await axios.put(`${api_path}/api/other/updateTopup/${id}`, formData, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'  // Important for sending files
          }
        });
      } else {
        response = await axios.post(`${api_path}/api/other/addTopup`, formData, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data'  // Important for sending files
          }
        });
      }
  
      if (response.status === 200) {
        toast.success(`${isEdit ? 'Top Up Updated' : 'Top Up Added'} successfully`);
        // Reset the form fields after success
        setCustomer('');
        setPhone('');
        setCPPhoneName('');
        setCPImei1('');
        setCPImei2('');
        setPhoneID('');
        setNIDA('');
        setCost('');
        setCloserUser('');
        setImage(null);  // Clear image
        navigate('/view-topups');
      }
    } catch (error) {
      console.error(error);
  
      if (error.response) {
        setError(error.response.data.message || 'An error occurred. Please try again.');
      } else if (error.request) {
        setError('Network error. Please check your connection.');
      } else {
        setError('An unexpected error occurred.');
      }
  
      toast.error(error.message || 'Failed to add return');
    }
  };

  if (loading) return <div>Loading...</div>;
  if (cashError) return <div>Error loading cash data: {cashError.message}</div>;
  if (phoneLoading) return <div>Phone Loading...</div>;
  
  const handleSelectChange = (value) => {
    // console.log('Selected value:', value, options);
    setPhoneID(value);
  };

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit' : 'Add'} TopUp Record</strong>
          </CCardHeader>
          <CCardBody>
            <CRow className="mb-3">
              {error && <p className="text-danger">{error}</p>}
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Jina la Mteja:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  value={customer}
                  onChange={(e) => setCustomer(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Namba ya Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="tel"
                  id="phone"
                  required
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Simu Ya Mteja (CP):
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="phone"
                  value={CPphoneName}
                  onChange={(e) => setCPPhoneName(e.target.value)}
                />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="imei1" className="col-sm-2 col-form-label">
               (CP) IMEI 1
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="imei1"
                  requirCP
                  value={CPimei1}
                  onChange={(e) => setCPImei1(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="imei2" className="col-sm-2 col-form-label">
               (CP) IMEI 2
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="imei2"
                  value={CPimei2}
                  onChange={(e) => setCPImei2(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Simu Anayochukua:
              </CFormLabel>
              <div className="col-sm-10">
                {/* <CFormSelect aria-label="Default select example" value={PhoneID} onChange={(e) => setPhoneID(e.target.value)}>
                    <option>Open this select menu</option>
                    {allPhone.map(phone => {
                      const last6Digits = phone.IMEI1.slice(-6); 
                      return (
                          <option key={phone.PhoneID} value={phone.PhoneID}>
                              {`${phone.PhoneName} - ${last6Digits}`}
                          </option>
                      );
                  })}
                  
                  </CFormSelect> */}
                    <Select
                      showSearch
                      placeholder="Select a phone"
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={structuredPhones}
                      onChange={handleSelectChange}
                      value={PhoneID ? { value: PhoneID } : undefined}
                    />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="imei2" className="col-sm-2 col-form-label">
               Kiasi Anachoongezea
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="imei2"
                  value={cost}
                  onChange={(e) => setCost(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="imei2" className="col-sm-2 col-form-label">
               Namba Ya Mtu Wa Karibu
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="imei2"
                  value={closerUser}
                  onChange={(e) => setCloserUser(e.target.value)}
                />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Namba ya NIDA:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="tel"
                  id="phone"
                  required
                  value={NIDA}
                  onChange={(e) => setNIDA(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Picha ya NIDA:
              </CFormLabel>
              <div className="col-sm-10">
                <Upload {...props}>
                  <Button icon={<UploadOutlined />}>Upload</Button>
                </Upload>
              </div>
            </CRow>
            <CRow className="w-25 mx-auto">
              <CButton color="primary" onClick={handleAdd}>
                {isEdit ? 'Submit' : 'Badilisha'}
              </CButton>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default AddTopUp;
