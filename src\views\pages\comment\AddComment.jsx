import React, { useEffect, useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CFormInput,
  CFormLabel,
  CButton, CFormSelect,
  CModal,CModalHeader,CModalTitle,
  CModalBody,CModalFooter,
  CFormTextarea
} from '@coreui/react';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';

import { Select } from 'antd';


const AddComment = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');

  const isEdit = id !== null;

  const navigate = useNavigate()
  
  const [submitted, setSubmitted] = useState(false);

    const [comment, setComment] = useState('');
  
  const [error, setError] = useState('');
  
  const handleAdd = async (e) => {
    e.preventDefault();

     // Prevent double submission
     if (submitted) return;

     setSubmitted(true);

        setError('');
    
    
      if (!comment) {
        setError('Please fill all required fields.');
        return;
      }
      
      const token = localStorage.getItem('joh_token');

      if (!token) {
        throw new Error('No token found');
      }
      
      const commentData = {
       comment
      };
      
      try {
        let response;
        
        // If we're editing, use PUT request, otherwise use POST
        if (isEdit) {
          response = await axios.put(`${api_path}/api/other/updateComment/${id}`, commentData, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
        } else {
          response = await axios.post(`${api_path}/api/other/addComment`, commentData, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
        }

        if (response.status === 200) {
          toast.success(`${isEdit ? 'A Comment Updated' : 'A Comment Added'} successfully`);
          setComment('');
          window.location.href = '/dashboard';
        }
      } catch (error) {
        console.error(error);
        
        if (error.response) {
          setError(error.response.data.message || 'An error occurred. Please try again.');
        } else if (error.request) {
          setError('Network error. Please check your connection.');
        } else {
          setError('An unexpected error occurred.');
        }
        
        toast.error(error.message || 'Failed to add phone');
      }finally {
        setSubmitted(false); 
      }
    };


  
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit' : 'Add'} Comment</strong>
          </CCardHeader>
          <CCardBody>
            <form action="" method="post" onSubmit={handleAdd}>
              {error && <p className="text-danger">{error}</p>}
              
              <CFormTextarea
                id="exampleFormControlTextarea1"
                label="Andika Maelezo hapa"
                rows={3}
                text=""
                onChange={(e) => setComment(e.target.value)}
                value={comment}
              ></CFormTextarea>

            <CRow className="w-25 mx-auto">
              <CButton color="primary" type="submit" disabled={submitted}>
                {submitted ? 'Submitting' : 'Submit'}
              </CButton>
            </CRow>
            </form>
          </CCardBody>
        </CCard>
      </CCol>

    </CRow>
  );
};

export default AddComment;
