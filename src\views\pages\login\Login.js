import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CForm,
  CFormInput,
  CInputGroup,
  CInputGroupText,
  CRow,
  CSpinner
} from '@coreui/react';
import CIcon from '@coreui/icons-react';
import { cilLockLocked, cilUser } from '@coreui/icons';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext';
import { api_path } from '../../../../environments';

const Login = () => {
  const { login, user } = useAuth();
  const navigate = useNavigate();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user != null) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const loginCredentials = {
        email: email,
        password: password
      };

      const response = await axios.post(
        `${api_path}/api/auth/login`,
        loginCredentials,
        { headers: { 'Content-Type': 'application/json' } }
      );

      if (response.status === 200) {
        const { token } = response.data;
        navigate('/dashboard');
        login(token);
      }
    } catch (error) {
      setIsLoading(false);
      setError(error.response ? error.response.data.message : error.message);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        width: '100%',
        maxWidth: '400px',
        background: 'white',
        borderRadius: 'var(--radius-2xl)',
        boxShadow: 'var(--shadow-xl)',
        padding: '40px',
        border: '1px solid var(--gray-200)'
      }}>
        {/* Logo Section */}
        <div className="text-center mb-8">
          <div style={{
            width: '80px',
            height: '80px',
            background: 'linear-gradient(135deg, var(--primary-500), var(--primary-600))',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 24px',
            color: 'white',
            fontSize: '32px',
            fontWeight: 'bold'
          }}>
            📊
          </div>
          <h1 style={{
            fontSize: 'var(--text-3xl)',
            fontWeight: '700',
            color: 'var(--gray-900)',
            marginBottom: '8px',
            fontFamily: 'var(--font-family-heading)'
          }}>
            Repo<span style={{ color: 'var(--primary-600)' }}>rta</span>
          </h1>
          <p style={{
            color: 'var(--gray-600)',
            fontSize: 'var(--text-base)',
            marginBottom: '0'
          }}>
            Welcome back! Please sign in to continue
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleLogin}>
          {error && (
            <div className="modern-alert alert-error mb-4">
              <strong>Error:</strong> {error}
            </div>
          )}

          <div className="modern-form-group">
            <label className="modern-form-label">
              <CIcon icon={cilUser} style={{ marginRight: '8px' }} />
              Email Address
            </label>
            <input
              type="email"
              className="modern-form-input"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              autoComplete="email"
              required
            />
          </div>

          <div className="modern-form-group">
            <label className="modern-form-label">
              <CIcon icon={cilLockLocked} style={{ marginRight: '8px' }} />
              Password
            </label>
            <input
              type="password"
              className="modern-form-input"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              autoComplete="current-password"
              required
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="btn-modern btn-primary-modern w-100"
            style={{
              padding: '12px 24px',
              fontSize: 'var(--text-base)',
              fontWeight: '600',
              marginTop: '8px'
            }}
          >
            {isLoading ? (
              <div className="d-flex align-items-center justify-content-center gap-2">
                <div className="loading-spinner"></div>
                Signing in...
              </div>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="text-center mt-6">
          <p style={{
            color: 'var(--gray-500)',
            fontSize: 'var(--text-sm)',
            marginBottom: '0'
          }}>
            © 2024 Reporta. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
