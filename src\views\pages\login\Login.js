import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CButton,
  CCard,
  CCardBody,
  CCardGroup,
  CCol,
  CContainer,
  CForm,
  CFormInput,
  CInputGroup,
  CInputGroupText,
  CRow,
  CSpinner
} from '@coreui/react';
import CIcon from '@coreui/icons-react';
import { cilLockLocked, cilUser } from '@coreui/icons';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext';
import { api_path } from '../../../../environments';

const Login = () => {
  const { login, user } = useAuth();
  const navigate = useNavigate();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user != null) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const loginCredentials = {
        email: email,
        password: password
      };

      const response = await axios.post(
        `${api_path}/api/auth/login`,
        loginCredentials,
        { headers: { 'Content-Type': 'application/json' } }
      );

      if (response.status === 200) {
        const { token } = response.data;
        navigate('/dashboard');
        login(token);
      }
    } catch (error) {
      setIsLoading(false);
      setError(error.response ? error.response.data.message : error.message);
    }
  };

  return (
    <div className="bg-light min-vh-100 d-flex flex-row align-items-center ">
    <CContainer className="w-100 w-lg-50">
    <CRow className="justify-content-center">
          <CCol md={8}>
            <div className="d-flex justify-content-center align-items-center ">
            <h1>Repo<span className='text-primary' style={{ fontFamily: 'cursive' }}>rta</span></h1>
            {/* <h2 className="text-primary" style={{ fontFamily: 'Poppins, sans-serif' }}>Repo<span>rta</span></h2> */}
            </div>
            <CCardGroup>
              <CCard className="p-4 shadow-lg rounded">
                <CCardBody>
                  <CForm onSubmit={handleLogin}>
                    <h2 className="text-center mb-4">Login</h2>
                    <p className="text-muted text-center">Sign In to your account</p>
                    {error && <p className="text-danger">{error}</p>}
                    <CInputGroup className="mb-3">
                      <CInputGroupText>
                        <CIcon icon={cilUser} />
                      </CInputGroupText>
                      <CFormInput
                        placeholder="Email"
                        autoComplete="email"
                        value={email}
                        type="email"
                        onChange={(e) => setEmail(e.target.value)}
                      />
                    </CInputGroup>
                    <CInputGroup className="mb-4">
                      <CInputGroupText>
                        <CIcon icon={cilLockLocked} />
                      </CInputGroupText>
                      <CFormInput
                        type="password"
                        placeholder="Password"
                        autoComplete="current-password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                      />
                    </CInputGroup>
                    <CRow className='d-flex justify-content-center'>
                      <CCol xs={6} >
                        {isLoading ? (
                          <CButton color="primary" className="px-4" disabled>
                            <CSpinner as="span" size="sm" aria-hidden="true" />
                            Loading...
                          </CButton>
                        ) : (
                          <CButton color="primary" className="px-4 w-100" type="submit">
                            Login
                          </CButton>
                        )}
                      </CCol>
                      {/* <CCol xs={6} className="text-right">
                        <CButton color="link" className="px-0">
                          Forgot password?
                        </CButton>
                      </CCol> */}
                    </CRow>
                  </CForm>
                </CCardBody>
              </CCard>
            </CCardGroup>
          </CCol>
        </CRow>
      </CContainer>
    </div>
  );
};

export default Login;
