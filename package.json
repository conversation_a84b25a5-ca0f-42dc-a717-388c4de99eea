{"name": "joh_report", "version": "1", "description": "JohShopping report", "homepage": "https://report.johshopping.com", "license": "MIT", "author": "Official Eric (https://github.com/officialeric)", "scripts": {"build": "vite build", "lint": "eslint \"src/**/*.js\"", "serve": "vite preview", "start": "vite"}, "dependencies": {"@coreui/chartjs": "^4.0.0", "@coreui/coreui": "^5.1.2", "@coreui/coreui-free-react-admin-template": "file:", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.3.0", "@coreui/react": "^5.4.0", "@coreui/react-chartjs": "^3.0.0", "@coreui/utils": "^2.0.2", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^6.1.5", "@mui/x-data-grid": "^7.22.0", "@popperjs/core": "^2.11.8", "@zxing/library": "^0.21.3", "antd": "^5.22.6", "axios": "^1.7.7", "chart.js": "^4.4.4", "classnames": "^2.5.1", "core-js": "^3.38.1", "dayjs": "^1.11.13", "joh_report": "file:", "jspdf": "^2.5.2", "lucide-react": "^0.474.0", "print-js": "^1.6.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-export-table": "^3.1.10", "react-icons": "^5.4.0", "react-imask": "^7.6.1", "react-redux": "^9.1.2", "react-responsive": "^10.0.0", "react-router-dom": "^6.26.2", "react-to-print": "^3.0.5", "react-toastify": "^10.0.6", "redux": "5.0.1", "simplebar-react": "^3.2.6", "socket.io-client": "^4.8.1", "sweetalert2": "^11.14.5"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.36.1", "eslint-plugin-react-hooks": "^4.6.2", "postcss": "^8.4.47", "prettier": "3.3.3", "sass": "^1.79.3", "vite": "^5.4.8"}}