import React from 'react'

import WidgetsDropdown from '../widgets/WidgetsDropdown'

const Dashboard = () => {

  return (
    <div className="fade-in">
      {/* Modern Dashboard Header */}
      <div className="mb-6">
        <h1 className="mb-2">Dashboard</h1>
        <p className="text-gray-600">
          Welcome back! Here's what's happening with your business today.
        </p>
      </div>

      {/* Dashboard Widgets */}
      <WidgetsDropdown className="mb-6" />

      {/* Quick Actions Section */}
      <div className="modern-card hover-lift">
        <div className="modern-card-header">
          <h4 className="mb-1">Quick Actions</h4>
          <p className="text-gray-500 mb-0">Frequently used actions for faster workflow</p>
        </div>
        <div className="modern-card-body">
          <div className="row">
            <div className="col-md-3 col-sm-6 mb-3">
              <a href="/add-cash" className="text-decoration-none">
                <div className="p-4 text-center hover-scale" style={{
                  background: 'var(--primary-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--primary-200)',
                  transition: 'all var(--transition-fast)'
                }}>
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>💰</div>
                  <h6 className="text-primary-700 mb-1">New Sale</h6>
                  <small className="text-gray-600">Record a cash transaction</small>
                </div>
              </a>
            </div>
            <div className="col-md-3 col-sm-6 mb-3">
              <a href="/add-return" className="text-decoration-none">
                <div className="p-4 text-center hover-scale" style={{
                  background: 'var(--warning-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--warning-200)',
                  transition: 'all var(--transition-fast)'
                }}>
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>↩️</div>
                  <h6 className="text-warning-700 mb-1">New Return</h6>
                  <small className="text-gray-600">Process a return</small>
                </div>
              </a>
            </div>
            <div className="col-md-3 col-sm-6 mb-3">
              <a href="/add-phone" className="text-decoration-none">
                <div className="p-4 text-center hover-scale" style={{
                  background: 'var(--success-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--success-200)',
                  transition: 'all var(--transition-fast)'
                }}>
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📱</div>
                  <h6 className="text-success-700 mb-1">Add Phone</h6>
                  <small className="text-gray-600">Add new inventory</small>
                </div>
              </a>
            </div>
            <div className="col-md-3 col-sm-6 mb-3">
              <a href="/view-cash" className="text-decoration-none">
                <div className="p-4 text-center hover-scale" style={{
                  background: 'var(--info-50)',
                  borderRadius: 'var(--radius-lg)',
                  border: '1px solid var(--info-200)',
                  transition: 'all var(--transition-fast)'
                }}>
                  <div style={{ fontSize: '32px', marginBottom: '8px' }}>📊</div>
                  <h6 className="text-info-700 mb-1">View Reports</h6>
                  <small className="text-gray-600">Check sales data</small>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
