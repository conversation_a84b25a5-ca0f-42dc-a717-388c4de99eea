import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { HomeOutlined, RightOutlined } from '@ant-design/icons';

const ModernBreadcrumb = ({ 
  customItems = null,
  showHome = true,
  className = "",
  separator = <RightOutlined style={{ fontSize: '12px', color: '#9CA3AF' }} />
}) => {
  const location = useLocation();

  // Route mapping for better breadcrumb names
  const routeMap = {
    '': 'Dashboard',
    'dashboard': 'Dashboard',
    'view-phones': 'Smart Phones',
    'view-small-phones': 'Basic Phones',
    'add-phone': 'Add Phone',
    'view-cash': 'Sales Records',
    'view-cash-small': 'Basic Phone Sales',
    'add-cash': 'New Sale',
    'view-returns': 'Returns',
    'add-return': 'New Return',
    'view-users': 'Users',
    'add-user': 'Add User',
    'single-record': 'Record Details',
    'profile': 'Profile',
    'settings': 'Settings'
  };

  // Generate breadcrumb items from current path
  const generateBreadcrumbs = () => {
    if (customItems) return customItems;

    const pathSegments = location.pathname.split('/').filter(segment => segment !== '');
    const breadcrumbs = [];

    // Add home if enabled
    if (showHome) {
      breadcrumbs.push({
        title: 'Dashboard',
        path: '/',
        icon: <HomeOutlined style={{ fontSize: '14px' }} />
      });
    }

    // Build breadcrumbs from path segments
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      // Get display name from route map or format segment
      const displayName = routeMap[segment] || 
        segment.split('-').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');

      breadcrumbs.push({
        title: displayName,
        path: currentPath,
        isActive: isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null; // Don't show breadcrumbs if only one item
  }

  return (
    <nav 
      className={`modern-breadcrumb ${className}`}
      style={{
        padding: 'var(--space-4) 0',
        marginBottom: 'var(--space-6)'
      }}
      aria-label="Breadcrumb"
    >
      <ol 
        className="breadcrumb-list"
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: 'var(--space-2)',
          margin: 0,
          padding: 0,
          listStyle: 'none'
        }}
      >
        {breadcrumbs.map((item, index) => (
          <li 
            key={item.path}
            className="breadcrumb-item"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--space-2)'
            }}
          >
            {index > 0 && (
              <span className="breadcrumb-separator">
                {separator}
              </span>
            )}
            
            {item.isActive ? (
              <span 
                className="breadcrumb-current"
                style={{
                  color: 'var(--gray-900)',
                  fontWeight: '600',
                  fontSize: 'var(--text-sm)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 'var(--space-1)'
                }}
                aria-current="page"
              >
                {item.icon}
                {item.title}
              </span>
            ) : (
              <Link
                to={item.path}
                className="breadcrumb-link"
                style={{
                  color: 'var(--gray-600)',
                  textDecoration: 'none',
                  fontSize: 'var(--text-sm)',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 'var(--space-1)',
                  padding: 'var(--space-1) var(--space-2)',
                  borderRadius: 'var(--radius-md)',
                  transition: 'all var(--transition-fast)'
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = 'var(--primary-600)';
                  e.target.style.background = 'var(--primary-50)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = 'var(--gray-600)';
                  e.target.style.background = 'transparent';
                }}
              >
                {item.icon}
                {item.title}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Page Header Component with Breadcrumbs
export const PageHeader = ({ 
  title, 
  subtitle, 
  breadcrumbs, 
  actions,
  className = "" 
}) => {
  return (
    <div className={`page-header ${className}`} style={{ marginBottom: 'var(--space-8)' }}>
      <ModernBreadcrumb customItems={breadcrumbs} />
      
      <div className="d-flex justify-content-between align-items-start">
        <div className="page-title-section">
          <h1 
            className="page-title"
            style={{
              fontSize: 'var(--text-3xl)',
              fontWeight: '700',
              color: 'var(--gray-900)',
              marginBottom: 'var(--space-2)',
              fontFamily: 'var(--font-family-heading)'
            }}
          >
            {title}
          </h1>
          {subtitle && (
            <p 
              className="page-subtitle"
              style={{
                fontSize: 'var(--text-base)',
                color: 'var(--gray-600)',
                margin: 0,
                maxWidth: '600px'
              }}
            >
              {subtitle}
            </p>
          )}
        </div>
        
        {actions && (
          <div className="page-actions d-flex gap-3 align-items-center">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

// Quick Navigation Component
export const QuickNav = ({ items = [], className = "" }) => {
  return (
    <div 
      className={`quick-nav ${className}`}
      style={{
        background: 'white',
        borderRadius: 'var(--radius-lg)',
        padding: 'var(--space-4)',
        boxShadow: 'var(--shadow-sm)',
        border: '1px solid var(--gray-200)',
        marginBottom: 'var(--space-6)'
      }}
    >
      <h6 
        style={{
          fontSize: 'var(--text-sm)',
          fontWeight: '600',
          color: 'var(--gray-700)',
          marginBottom: 'var(--space-3)',
          textTransform: 'uppercase',
          letterSpacing: '0.05em'
        }}
      >
        Quick Actions
      </h6>
      
      <div className="d-flex gap-2 flex-wrap">
        {items.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            className="quick-nav-item"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--space-2)',
              padding: 'var(--space-2) var(--space-3)',
              background: 'var(--gray-50)',
              border: '1px solid var(--gray-200)',
              borderRadius: 'var(--radius-md)',
              color: 'var(--gray-700)',
              textDecoration: 'none',
              fontSize: 'var(--text-sm)',
              fontWeight: '500',
              transition: 'all var(--transition-fast)'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'var(--primary-50)';
              e.target.style.borderColor = 'var(--primary-200)';
              e.target.style.color = 'var(--primary-700)';
              e.target.style.transform = 'translateY(-1px)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'var(--gray-50)';
              e.target.style.borderColor = 'var(--gray-200)';
              e.target.style.color = 'var(--gray-700)';
              e.target.style.transform = 'translateY(0)';
            }}
          >
            {item.icon && <span>{item.icon}</span>}
            {item.label}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ModernBreadcrumb;
