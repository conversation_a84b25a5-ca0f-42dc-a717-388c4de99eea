import React, { useEffect, useState } from 'react';
import {
    <PERSON>utton,
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow
} from '@coreui/react';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';
import useFetch from '../../../hooks/useFetch';
import dayjs from 'dayjs';
import { DatePicker, Select, Tooltip } from 'antd';
import axios from 'axios';
import { toast } from 'react-toastify';
import { api_path } from '../../../../environments';
import jsPDF from 'jspdf';
import { useAuth } from '../../../hooks/AuthContext';
import { FaFilePdf } from "react-icons/fa6";


const AllReport = () => {
const { role } = useAuth();
const isAdmin = role === 'admin';

  const [selectedUser, setSelectedUser] = useState();
  const [selectedUserName, setSelectedUserName] = useState();
  const [selectedDate, setSelectedDate] = useState();
  const [phones, setPhones] = useState([]);
  const { data: users } = useFetch('auth/users');

//   const { data: generalData } = useFetch('other/generalData');

  const structuredUsers = users?.map(user => ({
    value: user.UserID,  
    label: user.UserName,
  }));


  const handleSelectChange = (value, label) => {

    setSelectedUser(value);
    setSelectedUserName(label.label);
    setSelectedDate(null); 
    setPhones([]);         
};


const onDateChange = async (date, dateString) => {
    setSelectedDate(dateString);
    console.log(dateString);
    try {
        const response = await axios.get(`${api_path}/api/other/allData`,{
                params: {
                    user: selectedUser,
                    date: dateString
            } 
        });

        if (response.status === 200) {
            // console.log(response.data.data);
            setPhones(response.data.data);
            toast.success('Data fetched successfully!');
        }
        
    } catch (error) {
        console.log(error);
        toast.error('Error fetching data!');
        
    }
    console.log(data);
};


const exportToPDF = () => {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 10;
    const cellPadding = 2;
    const xStart = margin;
    const s_date = new Date(selectedDate);
    const options = { day: '2-digit', month: 'short', year: 'numeric' };
    const formattedDate = s_date.toLocaleDateString('en-GB', options).replace(/ /g, ' ');
    const title = `${selectedUserName} - ${formattedDate} - Phones`;
      
    // Define headers and data
    const headers = ["#", "PhoneName", "IMEI1", "IMEI2"];
    const dataForPdf = phones.map((phone,i) => [
      ++i || '',
      phone.PhoneName || '',
      phone.IMEI1 || '',
      phone.IMEI2 || ''
    ]);

    // Calculate column widths
    const columnWidths = headers.map((header, index) => {
    const headerWidth = doc.getStringUnitWidth(header) * doc.internal.getFontSize();
    const maxDataWidth = Math.max(...dataForPdf.map(row => {
      const cellValue = row[index] || ''; // Handle undefined/null values
      return doc.getStringUnitWidth(String(cellValue)) * doc.internal.getFontSize();
    }));
    return Math.max(headerWidth, maxDataWidth) + 2 * cellPadding;
  });
  
    // Adjust columns if they exceed page width
    const totalTableWidth = columnWidths.reduce((sum, width) => sum + width, 0);
    if (totalTableWidth > (pageWidth - 2 * margin)) {
      const scaleFactor = (pageWidth - 2 * margin) / totalTableWidth;
      columnWidths.forEach((_, i) => columnWidths[i] *= scaleFactor);
    }
  
    // Draw title and headers on current page
    const drawTitleAndHeaders = () => {
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(title, pageWidth / 2, 20, { align: "center" });
  
      doc.setFontSize(9);
      headers.forEach((header, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0) + cellPadding;
        doc.text(header, xPosition, 35); // Header text at Y=35
      });
  
      headers.forEach((_, i) => {
        const xPosition = xStart + columnWidths.slice(0, i).reduce((sum, w) => sum + w, 0);
        doc.rect(xPosition, 30, columnWidths[i], 10); // Header background
      });
    };
  
    // Initial page setup
    let currentPage = doc;
    let currentY = 40; // Start Y for data rows
    const cellHeight = 8;
    const maxY = pageHeight - 20; // Bottom margin
    let rowsOnCurrentPage = 0;
  
    // Draw initial headers
    drawTitleAndHeaders();
  
    for (let rowIndex = 0; rowIndex < dataForPdf.length; rowIndex++) {
      const row = dataForPdf[rowIndex];
  
      // Check if we need a new page
      if (currentY + (rowsOnCurrentPage + 1) * cellHeight > maxY) {
        currentPage.addPage();
        currentPage = currentPage;
        currentY = 40;
        rowsOnCurrentPage = 0;
        drawTitleAndHeaders();
      }
  
      // Draw row cells
      row.forEach((col, colIndex) => {
        const xPosition = xStart + columnWidths.slice(0, colIndex).reduce((sum, w) => sum + w, 0);
        const yPosition = currentY + (rowsOnCurrentPage * cellHeight);
  
        // Draw cell border
        doc.rect(xPosition, yPosition, columnWidths[colIndex], cellHeight);
  
        // Set font style and size
        doc.setFont("helvetica", "normal");
        let fontSize = 8;
        const availableWidth = columnWidths[colIndex] - 2 * cellPadding;
        let textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        
        // Adjust font size if needed
        while (textWidth > availableWidth && fontSize > 6) {
          fontSize -= 0.5;
          textWidth = doc.getStringUnitWidth(col.toString()) * fontSize;
        }
        doc.setFontSize(fontSize);
  
        // Draw cell content
        doc.text(col.toString(), xPosition + cellPadding, yPosition + cellHeight - 2);
      });
  
      rowsOnCurrentPage++;
    }
  
    doc.save(`${selectedUserName}_${selectedDate}_phones.pdf`);
  };


  
return (
    <CRow>
        <CCol xs={12}>
            <div className="d-flex justify-content-end align-items-center gap-2 mb-3">
            {phones.length > 0 && (
              
              <Tooltip title="Export To PDF">
                <CButton color="success" className="text-white" onClick={exportToPDF}>
                    <FaFilePdf /> 
                </CButton>
              </Tooltip>
            )}
            </div>
            
            <CCard className="mb-4">
                <CCardHeader>
                    <strong>Report</strong>
                </CCardHeader>
                <CCardBody>                        
                    {/* Select Input area */}
                    <div className="d-flex justify-content-around gap-3">

                    <Select
                        showSearch
                        className='w-75'
                        placeholder="Select User"
                        filterOption={(input, option) => {
                        var _a;
                        return (
                                (_a = option === null || option === void 0 ? void 0 : option.label) !== null &&
                                _a !== void 0
                                ? _a
                                : ''
                        )
                                .toLowerCase()
                                .includes(input.toLowerCase());
                        }}
                        options={structuredUsers}
                        onChange={handleSelectChange}
                    />
                    {selectedUser && (

                        <DatePicker
                        className=""
                        onChange={onDateChange}
                        value={selectedDate ? dayjs(selectedDate) : null}
                        />
                    )}
                    </div>


                    <CRow className="w-100 mt-3">
                            <div className="d-flex justify-content-between align-items-center">

                                    <div className="">
                                            <b>{phones.length}</b> phones
                                    </div>

                            </div>
                    </CRow>
                </CCardBody>
            </CCard>

            <CCard className="mb-4">
                    <CCardHeader>
                            <strong>User's Phones</strong>
                    </CCardHeader>
                    <CCardBody >
                        <div className="overflow-auto " style={{ maxHeight: '380px' }}>
                        {phones.length > 0 ? phones.map((phone,i) => (                                <>
                                <div className="d-flex justify-content-between align-items-center" key={phone.value}>
                                    <div className="">
                                            <span key={phone.PhoneID} className="me-2">
                                                  {++i}{'. '}{phone.PhoneName} - {phone.IMEI1.slice(-9)}
                                            </span>
                                    </div>
                                    <div className="">
                                    </div>
                                </div>
                                            <hr />
                                </>
                             )) 
                                    :
                            (
                                    <div className="text-center">
                                    <CIcon icon={icon.cilWarning} className="text-warning" style={{ fontSize: '5rem' }} />
                                    <h4 className="mt-3">No phone available</h4>
                                    <p>Please select User to View.</p>
                                    </div>
                            )}
                        </div>
                    </CCardBody> 
            </CCard>   
        </CCol>

    </CRow>
);
};

export default AllReport;
