  import React, { useEffect, useState } from 'react';
  import {
    CCard,
    CCardBody,
    CCardHeader,
    CCol,
    CRow,
    CFormInput,
    CFormLabel,
    CButton, CFormSelect,
    CModal,CModalHeader,CModalTitle,
    CModalBody,CModalFooter
  } from '@coreui/react';

  import CIcon from '@coreui/icons-react';
  import * as icon from '@coreui/icons';

  import axios from 'axios';
  import { api_path } from '../../../../environments';
  import { toast } from 'react-toastify';
  import { useLocation , useNavigate} from 'react-router-dom';
  import useFetch from '../../../hooks/useFetch';

  import { Select } from 'antd';

  
  const AddPhone = () => {
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const id = queryParams.get('id');
    const groupID = queryParams.get('groupID');
    const cPhoneID = queryParams.get('cPhoneID');
    const isEdit = id !== null;

    const navigate = useNavigate()
    const endpoint = isEdit ? 'other/singlePhone/' + id : cPhoneID ? 'other/singlePhone/' + cPhoneID : '';

    const { data: phone, loading, error: phoneError } = useFetch(endpoint);
    const { data: allGroups , loading : groupLoading } = useFetch('other/groups');
    
    const [submitted, setSubmitted] = useState(false);

    const [visible,setVisible] = useState(false)
    const [GroupID,setGroupID] = useState(false)
    const [phoneName, setPhoneName] = useState('');
    const [imei1, setImei1] = useState('');
    const [imei2, setImei2] = useState('');
    const [phoneCost, setPhoneCost] = useState('');
    const [gName, setGName] = useState('');

    const structuredGroups = allGroups?.map(group => ({
      value: group.GroupID,  
      label: group.GroupName,
    }));
    
    useEffect(() => {
      if (phone) {
        setPhoneName(phone.PhoneName);
        setImei1(phone.IMEI1);
        setImei2(phone.IMEI2);
        setPhoneCost(phone.PhonePrice);
        // setQuantity(phone.Quantity);
        setGroupID(phone.GroupID);
      }
    }, [phone]);

    useEffect(() => {
      if (groupID) {
        setGroupID(groupID);
      }
    },[groupID]);
    
    const [error, setError] = useState('');
    
    const handleAdd = async (e) => {
      e.preventDefault();
      // Prevent double submission
     if (submitted) return;

     setSubmitted(true);
      setError('');
      
        if (!phoneName || !imei1 || !phoneCost || !GroupID) {
          setError('Please fill all required fields.');
          return;
        }
        
        const token = localStorage.getItem('joh_token');

        if (!token) {
          throw new Error('No token found');
        }
        
        const phoneData = {
          phoneName,
          imei1,
          imei2,
          phoneCost,
          GroupID
        };
        
        try {
          let response;
          
          // If we're editing, use PUT request, otherwise use POST
          if (isEdit) {
            response = await axios.put(`${api_path}/api/other/updatePhone/${id}`, phoneData, {
              headers: {
                Authorization: `Bearer ${token}`
              }
            });
          } else {
            response = await axios.post(`${api_path}/api/other/addPhone`, phoneData, {
              headers: {
                Authorization: `Bearer ${token}`
              }
            });
          }

          if (response.status === 200) {
            toast.success(`${isEdit ? 'Phone Updated' : 'Phone Added'} successfully`);
            setPhoneName('');
            setImei1('');
            setImei2('');
            setPhoneCost('');
            setGroupID('');
            navigate('/view-phones');
          }
        } catch (error) {
          console.error(error);
          
          if (error.response) {
            setError(error.response.data.message || 'An error occurred. Please try again.');
          } else if (error.request) {
            setError('Network error. Please check your connection.');
          } else {
            setError('An unexpected error occurred.');
          }
          
          toast.error(error.message || 'Failed to add phone');
        }finally {
          setSubmitted(false); 
        }
      };

      const handleAddGroup =async () => {
        // console.log(PhoneID,pQuantity)
        const data = {
            gName
        }
        const response = await axios.post(`${api_path}/api/other/addGroup`,data);
    
        if(response.status == 200){
          toast.success("Group Added successfully");
          setVisible(false)
          setPQuantity('')
          window.location.reload();
        }
      }

     
    if (loading) return <div>Loading...</div>;
    if (phoneError) return <div>Error loading phone data: {cashError.message}</div>;
    if (groupLoading) return <div>Groups Loading...</div>;
    
    const handleSelectChange = (value) => {
      // console.log('Selected value:', value, options);
      setGroupID(value);
    };

    return (
      <CRow>
        <CCol xs={12}>
          <CCard className="mb-4">
            <CCardHeader>
              <strong>{isEdit ? 'Edit' : 'Add'} Phone</strong>
            </CCardHeader>
            <CCardBody>
              <form action="" method="post" onSubmit={handleAdd}>
              <CRow className="mb-3">
                {error && <p className="text-danger">{error}</p>}
                <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                  Jina la Simu:
                </CFormLabel>
                <div className="col-sm-10">
                  <CFormInput
                    type="text"
                    id="name"
                    value={phoneName}
                    onChange={(e) => setPhoneName(e.target.value)}
                  />
                </div>
              </CRow>
              <CRow className="mb-3">
                <CFormLabel htmlFor="imei1" className="col-sm-2 col-form-label">
                  IMEI 1
                </CFormLabel>
                <div className="col-sm-10">
                  <CFormInput
                    type="text"
                    id="imei1"
                    required
                    value={imei1}
                    onChange={(e) => setImei1(e.target.value)}
                  />
                </div>
              </CRow>
              <CRow className="mb-3">
                <CFormLabel htmlFor="imei2" className="col-sm-2 col-form-label">
                  IMEI 2
                </CFormLabel>
                <div className="col-sm-10">
                  <CFormInput
                    type="text"
                    id="imei2"
                    value={imei2}
                    onChange={(e) => setImei2(e.target.value)}
                  />
                </div>
              </CRow>

              <CRow className="mb-3">
                <CFormLabel htmlFor="bei" className="col-sm-2 col-form-label">
                  Bei ya simu
                </CFormLabel>
                <div className="col-sm-10">
                  <CFormInput
                    type="number"
                    id="bei"
                    value={phoneCost}
                    onChange={(e) => setPhoneCost(e.target.value)}
                  />
                </div>
              </CRow>
              
              <CRow className="mb-3">
                <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                  Kundi la simu:
                </CFormLabel>
                <div className="col-sm-10 d-flex gap-3">
                  {/* <CFormSelect aria-label="Default select example" value={GroupID} onChange={(e) => setGroupID(e.target.value)}>
                    <option>Open this select menu</option>
                    {allGroups && allGroups.length > 0 ? (
                        allGroups.map(group => (
                          <option key={group.GroupID} value={group.GroupID}>
                            {group.GroupName}
                          </option>
                        ))
                      ) : (
                        <option value="">No groups available</option>
                      )}
                  
                  </CFormSelect> */}
                    <Select
                      showSearch
                      placeholder="Select a phone Group"
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={structuredGroups}
                      onChange={handleSelectChange}
                      value={GroupID ? { value: GroupID } : undefined}
                    />
                  <CButton color="secondary" onClick={() =>{setVisible(!visible);}}>
                    <CIcon icon={icon.cilPlus} />
                  </CButton>
                </div>
              </CRow> 

              <CRow className="w-25 mx-auto">
                <CButton color="primary" type="submit" disabled={submitted}>
                {isEdit ? submitted ? 'Submitting' : 'Submit' : submitted ? 'Adding' : 'Add'}
                </CButton>
              </CRow>
              </form>
            </CCardBody>
          </CCard>
        </CCol>


        <CModal backdrop="static" visible={visible} onClose={() => setVisible(false)}>
          <CModalHeader>
          <CModalTitle>Add Group</CModalTitle>
          </CModalHeader>
          <CModalBody>
              <CRow className="mb-3">
                {/* {error && <p className="text-danger">{error}</p>} */}
                <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                  Name:
                </CFormLabel>
                <div className="col-sm-10">
                  <CFormInput
                    type="text"
                    id="name"
                    value={gName}
                    onChange={(e) => setGName(e.target.value)}
                  />
                </div>
              </CRow>
              
          </CModalBody>
          <CModalFooter>
          <CButton color="secondary" onClick={() => setVisible(false)}>
              Close
          </CButton>
          <CButton color="primary" onClick={() => handleAddGroup()}>Save changes</CButton>
          </CModalFooter>
          </CModal>
      </CRow>
    );
  };

  export default AddPhone;
