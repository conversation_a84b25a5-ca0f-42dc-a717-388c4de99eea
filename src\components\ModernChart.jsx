import React from 'react';
import { CChartLine, CChartBar, CChartDoughnut } from '@coreui/react-chartjs';

const ModernChart = ({ 
  type = 'line', 
  data, 
  title, 
  subtitle, 
  height = 300,
  gradient = true,
  animated = true,
  className = ''
}) => {
  // Enhanced chart options with modern styling
  const getChartOptions = () => {
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              family: 'Inter, sans-serif',
              size: 12,
              weight: '500'
            },
            color: '#6B7280'
          }
        },
        tooltip: {
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          titleColor: '#1F2937',
          bodyColor: '#6B7280',
          borderColor: '#E5E7EB',
          borderWidth: 1,
          cornerRadius: 8,
          padding: 12,
          displayColors: true,
          titleFont: {
            family: 'Inter, sans-serif',
            size: 14,
            weight: '600'
          },
          bodyFont: {
            family: 'Inter, sans-serif',
            size: 13
          }
        }
      },
      scales: type !== 'doughnut' ? {
        x: {
          grid: {
            display: false,
            drawBorder: false
          },
          ticks: {
            font: {
              family: 'Inter, sans-serif',
              size: 11
            },
            color: '#9CA3AF'
          }
        },
        y: {
          grid: {
            color: '#F3F4F6',
            drawBorder: false
          },
          ticks: {
            font: {
              family: 'Inter, sans-serif',
              size: 11
            },
            color: '#9CA3AF',
            padding: 10
          }
        }
      } : {},
      animation: animated ? {
        duration: 1000,
        easing: 'easeInOutQuart'
      } : false
    };

    return baseOptions;
  };

  // Enhanced data with gradients and modern colors
  const getEnhancedData = () => {
    if (!data) return { labels: [], datasets: [] };

    const enhancedData = { ...data };
    
    if (gradient && enhancedData.datasets) {
      enhancedData.datasets = enhancedData.datasets.map((dataset, index) => {
        const colors = [
          { primary: '#3B82F6', secondary: '#93C5FD' }, // Blue
          { primary: '#10B981', secondary: '#6EE7B7' }, // Green
          { primary: '#F59E0B', secondary: '#FCD34D' }, // Yellow
          { primary: '#EF4444', secondary: '#FCA5A5' }, // Red
          { primary: '#8B5CF6', secondary: '#C4B5FD' }, // Purple
          { primary: '#06B6D4', secondary: '#67E8F9' }  // Cyan
        ];
        
        const colorSet = colors[index % colors.length];
        
        return {
          ...dataset,
          backgroundColor: type === 'doughnut' 
            ? colors.map(c => c.primary)
            : `linear-gradient(180deg, ${colorSet.primary}20 0%, ${colorSet.primary}05 100%)`,
          borderColor: colorSet.primary,
          borderWidth: 2,
          pointBackgroundColor: colorSet.primary,
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6,
          tension: type === 'line' ? 0.4 : 0
        };
      });
    }

    return enhancedData;
  };

  const renderChart = () => {
    const chartProps = {
      data: getEnhancedData(),
      options: getChartOptions(),
      style: { height: `${height}px` }
    };

    switch (type) {
      case 'bar':
        return <CChartBar {...chartProps} />;
      case 'doughnut':
        return <CChartDoughnut {...chartProps} />;
      case 'line':
      default:
        return <CChartLine {...chartProps} />;
    }
  };

  return (
    <div className={`modern-chart-container ${className}`}>
      {(title || subtitle) && (
        <div className="chart-header mb-4">
          {title && (
            <h4 className="chart-title mb-1" style={{
              fontSize: 'var(--text-lg)',
              fontWeight: '600',
              color: 'var(--gray-900)',
              fontFamily: 'var(--font-family-heading)'
            }}>
              {title}
            </h4>
          )}
          {subtitle && (
            <p className="chart-subtitle mb-0" style={{
              fontSize: 'var(--text-sm)',
              color: 'var(--gray-600)'
            }}>
              {subtitle}
            </p>
          )}
        </div>
      )}
      
      <div className="chart-wrapper" style={{
        background: 'white',
        borderRadius: 'var(--radius-lg)',
        padding: 'var(--space-6)',
        boxShadow: 'var(--shadow-sm)',
        border: '1px solid var(--gray-200)'
      }}>
        {renderChart()}
      </div>
    </div>
  );
};

// Preset chart configurations for common use cases
export const SalesChart = ({ data, ...props }) => (
  <ModernChart
    type="line"
    title="Sales Overview"
    subtitle="Monthly sales performance"
    data={data}
    {...props}
  />
);

export const InventoryChart = ({ data, ...props }) => (
  <ModernChart
    type="bar"
    title="Inventory Status"
    subtitle="Current stock levels by category"
    data={data}
    {...props}
  />
);

export const CategoryChart = ({ data, ...props }) => (
  <ModernChart
    type="doughnut"
    title="Sales by Category"
    subtitle="Distribution of sales across product categories"
    data={data}
    height={250}
    {...props}
  />
);

export default ModernChart;
