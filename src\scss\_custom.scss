// Modern Design System Foundation
// ================================

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

// Design Tokens
:root {
  // Modern Color Palette
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  // Semantic Colors
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  --info-50: #f0f9ff;
  --info-500: #06b6d4;
  --info-600: #0891b2;

  // Neutral Colors
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  // Typography
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  // Font Sizes
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  // Spacing
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  // Border Radius
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  // Shadows
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

// Global Typography
body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-700);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.3;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

// Modern Card Styles
.modern-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }
}

.modern-card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.modern-card-body {
  padding: var(--space-6);
}

// Modern Button Styles
.btn-modern {
  font-family: var(--font-family-primary);
  font-weight: 500;
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-6);
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-primary-modern {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;

  &:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  }
}

.btn-success-modern {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;

  &:hover {
    background: linear-gradient(135deg, var(--success-600), var(--success-700));
  }
}

.btn-secondary-modern {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);

  &:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
  }
}

// Modern Form Styles
.modern-form-group {
  margin-bottom: var(--space-6);
}

.modern-form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
  display: block;
  font-size: var(--text-sm);
}

.modern-form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  background: white;

  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  }

  &::placeholder {
    color: var(--gray-400);
  }
}

// Modern Table Styles
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);

  th {
    background: var(--gray-50);
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--text-sm);
    border-bottom: 1px solid var(--gray-200);
  }

  td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-600);

    &:last-child {
      border-bottom: none;
    }
  }

  tr {
    transition: background-color var(--transition-fast);

    &:hover {
      background: var(--gray-50);
    }
  }
}

// Modern Dashboard Cards
.dashboard-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  }
}

.dashboard-card-title {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--gray-600);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dashboard-card-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

// Modern Navigation
.modern-sidebar {
  background: white;
  border-right: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.modern-nav-item {
  padding: var(--space-3) var(--space-6);
  color: var(--gray-600);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  margin: var(--space-1) var(--space-4);

  &:hover {
    background: var(--primary-50);
    color: var(--primary-600);
  }

  &.active {
    background: var(--primary-100);
    color: var(--primary-700);
    font-weight: 500;
  }
}

// Loading States
.modern-loading {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Modern Alerts
.modern-alert {
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-md);
  border: 1px solid;
  margin-bottom: var(--space-4);

  &.alert-success {
    background: var(--success-50);
    border-color: var(--success-200);
    color: var(--success-800);
  }

  &.alert-error {
    background: var(--error-50);
    border-color: var(--error-200);
    color: var(--error-800);
  }

  &.alert-warning {
    background: var(--warning-50);
    border-color: var(--warning-200);
    color: var(--warning-800);
  }

  &.alert-info {
    background: var(--info-50);
    border-color: var(--info-200);
    color: var(--info-800);
  }
}

// Enhanced Mobile Responsiveness
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-full-width {
    width: 100% !important;
  }

  .mobile-stack {
    flex-direction: column !important;
    gap: var(--space-4) !important;
  }

  .dashboard-card {
    padding: var(--space-4);
    margin-bottom: var(--space-4);
  }

  .modern-card-header,
  .modern-card-body {
    padding: var(--space-4);
  }

  .modern-table {
    font-size: var(--text-sm);

    th, td {
      padding: var(--space-2) var(--space-3);
    }
  }

  // Mobile-specific button adjustments
  .btn-modern {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);

    &.mobile-full {
      width: 100%;
      margin-bottom: var(--space-2);
    }
  }

  // Mobile search bar
  .modern-search-bar {
    padding: var(--space-4);

    .filter-controls {
      flex-direction: column;
      gap: var(--space-3);

      > * {
        width: 100%;
      }
    }
  }

  // Mobile form improvements
  .modern-form-input,
  .modern-form-select {
    font-size: 16px; // Prevents zoom on iOS
    padding: var(--space-4);
  }

  // Mobile table - card view
  .mobile-table-card {
    display: block;

    .modern-table {
      display: none;
    }

    .table-card-item {
      background: white;
      border-radius: var(--radius-lg);
      padding: var(--space-4);
      margin-bottom: var(--space-3);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);

      .card-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: var(--space-3);
        padding-bottom: var(--space-3);
        border-bottom: 1px solid var(--gray-200);
      }

      .card-content {
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: var(--space-2);

          .label {
            font-weight: 500;
            color: var(--gray-600);
            font-size: var(--text-sm);
          }

          .value {
            color: var(--gray-900);
            font-size: var(--text-sm);
          }
        }
      }

      .card-actions {
        margin-top: var(--space-3);
        padding-top: var(--space-3);
        border-top: 1px solid var(--gray-200);
        display: flex;
        gap: var(--space-2);
        justify-content: flex-end;
      }
    }
  }

  // Mobile pagination
  .modern-pagination {
    flex-wrap: wrap;
    gap: var(--space-1);

    .page-item {
      padding: var(--space-2);
      font-size: var(--text-sm);
      min-width: 40px;
      text-align: center;
    }
  }

  // Mobile dashboard adjustments
  .dashboard-card-value {
    font-size: var(--text-2xl);
  }

  .dashboard-card-title {
    font-size: var(--text-xs);
  }
}

// Tablet specific adjustments
@media (min-width: 769px) and (max-width: 1024px) {
  .dashboard-card {
    padding: var(--space-5);
  }

  .modern-table {
    th, td {
      padding: var(--space-3) var(--space-4);
    }
  }
}

// Large screen optimizations
@media (min-width: 1200px) {
  .dashboard-card {
    padding: var(--space-8);
  }

  .modern-card-body {
    padding: var(--space-8);
  }

  .modern-search-bar {
    padding: var(--space-8);
  }
}

// Dark Mode Support
// =================

[data-theme="dark"] {
  // Dark Mode Color Palette
  --primary-50: #1e3a8a;
  --primary-100: #1e40af;
  --primary-200: #2563eb;
  --primary-300: #3b82f6;
  --primary-400: #60a5fa;
  --primary-500: #93c5fd;
  --primary-600: #bfdbfe;
  --primary-700: #dbeafe;
  --primary-800: #eff6ff;
  --primary-900: #f8fafc;

  // Dark Neutral Colors
  --gray-50: #0f172a;
  --gray-100: #1e293b;
  --gray-200: #334155;
  --gray-300: #475569;
  --gray-400: #64748b;
  --gray-500: #94a3b8;
  --gray-600: #cbd5e1;
  --gray-700: #e2e8f0;
  --gray-800: #f1f5f9;
  --gray-900: #f8fafc;

  // Dark Semantic Colors
  --success-50: #064e3b;
  --success-500: #10b981;
  --success-600: #34d399;

  --warning-50: #451a03;
  --warning-500: #f59e0b;
  --warning-600: #fbbf24;

  --error-50: #450a0a;
  --error-500: #ef4444;
  --error-600: #f87171;

  --info-50: #0c4a6e;
  --info-500: #06b6d4;
  --info-600: #22d3ee;
}

// Dark Mode Component Styles
[data-theme="dark"] {
  body {
    background: var(--gray-50);
    color: var(--gray-700);
  }

  .modern-card {
    background: var(--gray-100);
    border-color: var(--gray-300);
    color: var(--gray-700);
  }

  .modern-card-header {
    background: var(--gray-200);
    border-color: var(--gray-300);
  }

  .dashboard-card {
    background: var(--gray-100);
    border-color: var(--gray-300);

    &::before {
      background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    }
  }

  .modern-table {
    background: var(--gray-100);

    th {
      background: var(--gray-200);
      color: var(--gray-700);
      border-color: var(--gray-300);
    }

    td {
      border-color: var(--gray-300);
      color: var(--gray-600);
    }

    tr:hover {
      background: var(--gray-200);
    }
  }

  .modern-form-input {
    background: var(--gray-100);
    border-color: var(--gray-300);
    color: var(--gray-700);

    &:focus {
      border-color: var(--primary-500);
      background: var(--gray-50);
    }

    &::placeholder {
      color: var(--gray-400);
    }
  }

  .modern-search-bar {
    background: var(--gray-100);
    border-color: var(--gray-300);
  }

  .btn-modern {
    &.btn-primary-modern {
      background: linear-gradient(135deg, var(--primary-600), var(--primary-700));

      &:hover {
        background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
      }
    }

    &.btn-secondary-modern {
      background: var(--gray-200);
      color: var(--gray-700);
      border-color: var(--gray-400);

      &:hover {
        background: var(--gray-300);
        border-color: var(--gray-500);
      }
    }
  }

  .modern-alert {
    &.alert-success {
      background: var(--success-50);
      border-color: var(--success-300);
      color: var(--success-700);
    }

    &.alert-error {
      background: var(--error-50);
      border-color: var(--error-300);
      color: var(--error-700);
    }

    &.alert-warning {
      background: var(--warning-50);
      border-color: var(--warning-300);
      color: var(--warning-700);
    }

    &.alert-info {
      background: var(--info-50);
      border-color: var(--info-300);
      color: var(--info-700);
    }
  }
}

// Theme Toggle Component Styles
.theme-toggle {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: var(--primary-500);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
  }

  &:active {
    transform: scale(0.95);
  }
}

// Smooth theme transitions
* {
  transition: background-color var(--transition-normal),
              border-color var(--transition-normal),
              color var(--transition-normal);
}

// Disable transitions during theme change to prevent flashing
.theme-transitioning * {
  transition: none !important;
}

// Utility Classes
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-900 { color: var(--gray-900); }

.text-primary { color: var(--primary-600); }
.text-success { color: var(--success-600); }
.text-warning { color: var(--warning-600); }
.text-error { color: var(--error-600); }

.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-white { background-color: white; }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }

.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }

.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }

.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }

// Modern Search and Filter Bar
.modern-search-bar {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  margin-bottom: var(--space-6);

  .search-input {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);

    &:focus {
      outline: none;
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
    }
  }

  .filter-controls {
    display: flex;
    gap: var(--space-3);
    align-items: center;
    flex-wrap: wrap;
  }
}

// Modern Pagination
.modern-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-6);

  .page-item {
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);

    &:hover {
      background: var(--gray-50);
      border-color: var(--gray-400);
    }

    &.active {
      background: var(--primary-500);
      border-color: var(--primary-500);
      color: white;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background: white;
        border-color: var(--gray-300);
      }
    }
  }
}

// Enhanced Loading States & Animations
.skeleton-loader {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.bounce-in {
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

// Modern Card Hover Effects
.hover-lift {
  transition: all var(--transition-normal);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
  }
}

.hover-scale {
  transition: transform var(--transition-fast);

  &:hover {
    transform: scale(1.02);
  }
}

// Enhanced Button States
.btn-modern {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }
}

// Modern Progress Indicators
.progress-modern {
  height: 8px;
  background: var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;

  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-lg);
    transition: width var(--transition-normal);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      animation: shimmer 2s infinite;
    }
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// Modern Toast Notifications
.toast-modern {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-6);
  margin-bottom: var(--space-3);
  animation: slideInRight 0.3s ease-out;

  &.toast-success {
    border-left: 4px solid var(--success-500);
  }

  &.toast-error {
    border-left: 4px solid var(--error-500);
  }

  &.toast-warning {
    border-left: 4px solid var(--warning-500);
  }

  &.toast-info {
    border-left: 4px solid var(--info-500);
  }
}

// Enhanced Table Animations
.modern-table {
  tbody tr {
    transition: all var(--transition-fast);

    &:hover {
      background: var(--primary-50);
      transform: scale(1.01);
    }
  }

  .table-row-enter {
    opacity: 0;
    transform: translateY(20px);
  }

  .table-row-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease-out;
  }
}

// Modern Modal/Drawer Animations
.modal-backdrop-modern {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

.modal-content-modern {
  animation: bounceIn 0.4s ease-out;
  border-radius: var(--radius-xl);
  border: none;
  box-shadow: var(--shadow-xl);
}

// Pulse Animation for Important Elements
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

// Modern Focus States
.modern-form-input:focus,
.modern-form-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

// Smooth Page Transitions
.page-transition-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s ease-out;
}
