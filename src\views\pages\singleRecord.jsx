import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CButton,
  CCardFooter
} from '@coreui/react';
// import { toast } from 'react-toastify';
// import axios from 'axios';
import useFetch from '../../hooks/useFetch'; 
import { useAuth } from '../../hooks/AuthContext';
import { useLocation } from 'react-router-dom';
import { jsPDF} from 'jspdf';
import { FaFilePdf } from 'react-icons/fa6';
import { Tooltip , Image } from 'antd';
import { api_path } from '../../../environments';

const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'Tsh' }).format(value);
}

// Utility function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: false
  });
};

const SingleRecord = () => {
  const { role } = useAuth();
  const isAdmin = role === 'admin';
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const type = queryParams.get('type');
  const id = queryParams.get('id');
  const [endPoint, setEndPoint] = useState('');

  useEffect(() => {
    if (type === 'cash') {
      setEndPoint('singleCash/' + id);
    } else if (type === 'return') {
      setEndPoint('singleReturn/' + id);
    } else if (type === 'topup') {
      setEndPoint('singleTopup/' + id);
    }else if (type === 'cashsmall') {
      setEndPoint('singleSmallCash/' + id);
    }
  }, [type]);

  const { data: singleData, loading, error } = useFetch('other/' + endPoint);
  // console.log(singleData);

  const downloadPDF = () => {
    if (!singleData) return;

    const doc = new jsPDF();
    
    doc.setFontSize(16);
    doc.text(`Top-Up Record - ${singleData.CustomerName}`, 20, 20);
    
    doc.setFontSize(12);
    doc.text(`Customer Name: ${singleData.CustomerName}`, 20, 30);
    doc.text(`Phone Number: ${singleData.PhoneNumber}`, 20, 40);
    doc.text(`NIDA: ${singleData.CustomerNIDA}`, 20, 50);
    doc.text(`----------------------------------------------------------------`, 20, 55);
    doc.text(`Customer Phone (CP): ${singleData.CustomerPhone}`, 20, 60);
    doc.text(`(CP) IMEI 1: ${singleData.CPIMEI1}`, 20, 70);
    doc.text(`(CP) IMEI 2: ${singleData.CPIMEI2}`, 20, 80);
    doc.text(`Mtu wa karibu: ${singleData.CloserUserPhone}`, 20, 90);
    doc.text(`----------------------------------------------------------------`, 20, 95);
    doc.text(`Phone Name (NP): ${singleData.PhoneName}`, 20, 100);
    doc.text(`(NP) IMEI 1: ${singleData.IMEI1}`, 20, 110);
    doc.text(`(NP) IMEI 2: ${singleData.IMEI2}`, 20, 120);
    doc.text(`Top-Up Cost: ${formatCurrency(singleData.TopUpCost)}`, 20, 130);
    
    doc.text(`Date: ${formatDate(singleData.created_at)}`, 20, 150);
    doc.text(`Agent: ${singleData.UserName}`, 20, 160);

    doc.save(`${singleData.CustomerName}_topup_record.pdf`);
  };

  const handleDelete = (recordID) => {
    console.log(`Deleting record with ID: ${recordID}`);
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading data: {error.message}</div>;

  return (
    <CRow>
      <CCol xs={12}>
      {type === 'topup' && singleData && (
        <div className="my-2 d-flex justify-content-between">
              <div className=""></div>
              <div className="">
                <Tooltip title="Download PDF">
                  <CButton color="success" className="text-white" onClick={downloadPDF}>
                      <FaFilePdf /> 
                  </CButton>
                </Tooltip>
              </div>
        </div>
      )}
        <CCard className="mb-4">
          <CCardHeader>
            <strong>Record Detail</strong>
          </CCardHeader>
          <CCardBody>
            {type === 'cash' && singleData && (
              <>
                <CRow>
                  {/* Left Column (Cash Titles) */}
                  <CCol md={6}>
                    <p><strong>Customer Name:</strong> &nbsp; {singleData.CustomerName}</p>
                    <p><strong>Phone Number:</strong> &nbsp; <a href={`tel:${singleData.CustomerPhoneNumber}`}>{singleData.CustomerPhoneNumber}</a></p>
                    <p><strong>Sold Phone:</strong> &nbsp; {singleData.PhoneName}</p>
                    <p><strong>Selling Price:</strong> &nbsp; {formatCurrency(singleData.SellingPrice)}</p>
                  </CCol>

                  {/* Right Column (Cash Data) */}
                  <CCol md={6}>
                    <p><strong>IMEI 1:</strong> &nbsp; {singleData.IMEI1}</p>
                    <p><strong>IMEI 2:</strong> &nbsp; {singleData.IMEI2}</p>
                    <p><strong>Date:</strong> &nbsp; {formatDate(singleData.muda)}</p>
                    <p><strong>Agent:</strong> &nbsp; {singleData.UserName}</p>
                  </CCol>
                </CRow>

                {/* Only show the delete button for admins */}
                {/* {isAdmin && (
                  <CButton color="danger" 
                  onClick={() => {
                    const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                    
                    if (isConfirmed) {
                      handleDelete(singleData.CSID);
                    }
                  }}
                  >
                    Delete Record
                  </CButton>
                )} */}
              </>
            )}
            {type === 'cashsmall' && singleData && (
              <>
                <CRow>
                  {/* Left Column (Cash Titles) */}
                  <CCol md={6}>
                    <p><strong>Customer Name:</strong> &nbsp; {singleData.CustomerName}</p>
                    <p><strong>Phone Number:</strong> &nbsp; <a href={`tel:${singleData.CustomerPhoneNumber}`}>{singleData.CustomerPhoneNumber}</a></p>
                    <p><strong>Sold Phone:</strong> &nbsp; {singleData.PhoneName}</p>
                    <p><strong>Selling Price:</strong> &nbsp; {formatCurrency(singleData.SellingPrice)}</p>
                  </CCol>

                  {/* Right Column (Cash Data) */}
                  <CCol md={6}>
                    <p><strong>Date:</strong> &nbsp; {singleData.muda ? formatDate(singleData.muda) : 'No Record'}</p>
                    <p><strong>Agent:</strong> &nbsp; {singleData.UserName}</p>
                  </CCol>
                </CRow>

                {/* Only show the delete button for admins */}
                {/* {isAdmin && (
                  <CButton color="danger" 
                  onClick={() => {
                    const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                    
                    if (isConfirmed) {
                      handleDelete(singleData.CSID);
                    }
                  }}
                  >
                    Delete Record
                  </CButton>
                )} */}
              </>
            )}

            {type === 'return' && singleData && (
              <>
                <CRow>
                  {/* Left Column (Return Titles) */}
                  <CCol md={6}>
                    <p><strong>Customer Name:</strong> &nbsp; {singleData.CustomerName}</p>
                    <p><strong>Phone Number:</strong> &nbsp; <a href={`tel:${singleData.CloserUserPhone}`}>{singleData.PhoneNumber}</a></p>
                    <p><strong>Iliyorudishwa(RP):</strong> &nbsp; {singleData.RPPhoneName}</p>
                    <p><strong>(RP) IMEI 1:</strong> &nbsp; {singleData.TPIMEI1}</p>
                    <p><strong>(RP) IMEI 2:</strong> &nbsp; {singleData.TPIMEI2}</p>
                  </CCol>

                  {/* Right Column (Return Data) */}
                  <CCol md={6}>
                    <p><strong>Iliyochukuliwa(TP):</strong> &nbsp; {singleData.TPPhoneName}</p>
                    <p><strong>(TP) IMEI 1:</strong> &nbsp; {singleData.RPIMEI1}</p>
                    <p><strong>(TP) IMEI 2:</strong> &nbsp; {singleData.RPIMEI2}</p>
                    <p><strong>Date:</strong> &nbsp; {formatDate(singleData.created_at)}</p>
                    <p><strong>Sababu:</strong> &nbsp; {singleData.comment}</p>
                    <p><strong>Agent:</strong> &nbsp; {singleData.UserName}</p>
                  </CCol>
                </CRow>

                {/* Only show the delete button for admins */}
                {/* {isAdmin && (
                    <CButton color="danger" 
                    onClick={() => {
                      const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                      
                      if (isConfirmed) {
                        handleDelete(singleData.ReturnID);
                      }
                    }}
                    >
                      Delete Record
                    </CButton>
                  
                )} */}
              </>
            )}

            {type === 'topup' && singleData && (
              <>
                <CRow>
                  {/* Left Column (Top-Up Titles) */}
                  <CCol md={6}>
                    <p><strong>Customer Name:</strong> &nbsp; {singleData.CustomerName}</p>
                    <p><strong>Phone Number:</strong> &nbsp; <a href={`tel:${singleData.PhoneNumber}`}>{singleData.PhoneNumber}</a></p>
                    <p><strong>NIDA:</strong> &nbsp;{singleData.CustomerNIDA}</p>
                    <p><strong>Needed Phone(NP):</strong> &nbsp; {singleData.PhoneName}</p>
                    <p><strong>(NP) IMEI 1:</strong> &nbsp; {singleData.IMEI1}</p>
                    <p><strong>(NP) IMEI 2:</strong> &nbsp; {singleData.IMEI2}</p>
                    <p><strong>Top-Up Cost:</strong> &nbsp; {formatCurrency(singleData.TopUpCost)}</p>
                  </CCol>

                  {/* Right Column (Top-Up Data) */}
                  <CCol md={6}>
                    <p><strong>Customer Phone(CP):</strong> &nbsp; {singleData.CustomerPhone}</p>
                    <p><strong>(CP) IMEI 1:</strong> &nbsp; {singleData.CPIMEI1}</p>
                    <p><strong>(CP) IMEI 2:</strong> &nbsp; {singleData.CPIMEI2}</p>
                    <p><strong>Mtu wa karibu:</strong> &nbsp; <a href={`tel:${singleData.PhoneNumber}`}>{singleData.CloserUserPhone}</a> </p>
                    <p><strong>Date:</strong> &nbsp; {formatDate(singleData.created_at)}</p>
                    <p><strong>Agent:</strong> &nbsp; {singleData.UserName}</p>
                    <Image width={200} src={`${api_path}/uploads/${singleData.image}`}/>
                  </CCol>
                </CRow>

                {/* Only show the delete button for admins */}
                {/* {isAdmin && (
                 <CButton color="danger" 
                 onClick={() => {
                   const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                   
                   if (isConfirmed) {
                     handleDelete(singleData.TopUpID);
                   }
                 }}
                 >
                   Delete Record
                 </CButton>
                )} */}
              </>
            )}
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default SingleRecord;
