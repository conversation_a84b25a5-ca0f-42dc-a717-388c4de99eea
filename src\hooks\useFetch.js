import { useState, useEffect } from 'react';
import axios from 'axios';
import { api_path } from '../../environments';

const useFetch = (endpoint) => {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      setLoading(true);
      try {
        const token = localStorage.getItem('joh_token');
        
        if (!token) {
          throw new Error('No token found');
        }

        const response = await axios.get(`${api_path}/api/${endpoint}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (isMounted) {
          setData(response.data.data); 
        }
      } catch (err) {
        if (isMounted) {
          setError(err.response ? err.response.data.message : err.message);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    // Cleanup to avoid state updates on unmounted component
    return () => {
      isMounted = false;
    };
  }, [endpoint]);

  return { data, loading, error };
};

export default useFetch;
