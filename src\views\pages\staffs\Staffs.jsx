import React, { useState, useEffect } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CAvatar,
  CButton,
  CFormInput,
  CPagination,
  CPaginationItem
} from '@coreui/react';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import avatar from '../../../assets/images/avatars/2.jpg';
import useFetch from '../../../hooks/useFetch'; 
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'
import { Link } from 'react-router-dom';

const Staffs = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5; 

  const { data: users , loading , error} = useFetch('auth/users');
  const initialStaffs = Array.isArray(users)
  ? users.map(user => ({
      id: user.UserID,
      name: user.UserName,
      email: user.Email,
      phone: user.PhoneNumber || '', 
      hasProfile: false
  }))
  : [];


  const filteredStaffs = initialStaffs.filter(staff =>
    staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.phone.includes(searchTerm)
  );

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDeleteUser =async (userID) => {
    const response = await axios.post(`${api_path}/api/auth/deleteUser/${userID}`);

    if(response.status == 200){
      toast.success("User Deleted successfully");
      window.location.reload();
    }
  }

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading users: {error.message}</div>;

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>All Users</strong>
            <div className='d-flex gap-2'>
              <CFormInput
                type="text"
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable responsive>
              <CTableHead>
                <CTableRow>
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Profile</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Email</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Phone</CTableHeaderCell>

                  {isAdmin && (

                  <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                  )}
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStaffs.map((staff,index) => (
                  <CTableRow key={staff.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>
                      {staff.hasProfile ? (
                        <CAvatar src={avatar} />
                      ) : (
                        <CAvatar color="secondary">{staff.name.substring(0, 2).toUpperCase()}</CAvatar>
                      )}
                    </CTableDataCell>
                    <CTableDataCell>{staff.name}</CTableDataCell>
                    <CTableDataCell>{staff.email}</CTableDataCell>
                    <CTableDataCell>
                      <a href={`tel:${staff.phone}`}>{staff.phone}</a>
                    </CTableDataCell>

                    {isAdmin && (

                      <CTableDataCell className='d-flex gap-2'>
                        {/* <CButton color="secondary">
                          <CIcon icon={icon.cilOptions} />
                        </CButton> */}
                        <Link to={'/add-users?id=' + staff.id}>
                        <CButton color="primary">
                          <CIcon icon={icon.cilPenAlt} />
                        </CButton>
                      </Link>
                        <CButton 
                            color="danger" 
                            className='text-white' 
                            onClick={() => {
                              const isConfirmed = window.confirm("Are you sure you want to delete this user?");
                              
                              if (isConfirmed) {
                                handleDeleteUser(staff.id)
                              }
                            }}
                          >
                          <CIcon icon={icon.cilTrash} />
                        </CButton>
                      </CTableDataCell>
                    )}
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default Staffs;
