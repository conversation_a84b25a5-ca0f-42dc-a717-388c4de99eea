import React, { useState, useEffect, useMemo } from 'react';
import { DatePicker, Select, Input, Button, Space, Tag, Tooltip } from 'antd';
import { SearchOutlined, FilterOutlined, ClearOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

const AdvancedSearch = ({
  data = [],
  onFilteredDataChange,
  searchFields = [],
  filterFields = [],
  exportOptions = {},
  placeholder = "Search...",
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState(null);
  const [activeFilters, setActiveFilters] = useState({});
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Debounced search
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Advanced filtering logic
  const filteredData = useMemo(() => {
    let filtered = [...data];

    // Text search
    if (debouncedSearchTerm) {
      filtered = filtered.filter(item => {
        return searchFields.some(field => {
          const value = getNestedValue(item, field);
          return value && value.toString().toLowerCase().includes(debouncedSearchTerm.toLowerCase());
        });
      });
    }

    // Date range filter
    if (dateRange && dateRange.length === 2) {
      filtered = filtered.filter(item => {
        const itemDate = dayjs(item.date);
        return itemDate.isAfter(dateRange[0].startOf('day')) && 
               itemDate.isBefore(dateRange[1].endOf('day'));
      });
    }

    // Custom filters
    Object.entries(activeFilters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        filtered = filtered.filter(item => {
          const itemValue = getNestedValue(item, key);
          if (Array.isArray(value)) {
            return value.includes(itemValue);
          }
          return itemValue === value;
        });
      }
    });

    // Sorting
    if (sortConfig.key) {
      filtered.sort((a, b) => {
        const aValue = getNestedValue(a, sortConfig.key);
        const bValue = getNestedValue(b, sortConfig.key);
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [data, debouncedSearchTerm, dateRange, activeFilters, sortConfig, searchFields]);

  // Helper function to get nested object values
  const getNestedValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // Get unique values for filter options
  const getFilterOptions = (field) => {
    const values = data.map(item => getNestedValue(item, field))
      .filter(value => value !== null && value !== undefined && value !== '');
    return [...new Set(values)].sort();
  };

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setActiveFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setDateRange(null);
    setActiveFilters({});
    setSortConfig({ key: null, direction: 'asc' });
  };

  // Handle sorting
  const handleSort = (field) => {
    setSortConfig(prev => ({
      key: field,
      direction: prev.key === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Export functionality
  const handleExport = (format) => {
    if (exportOptions.onExport) {
      exportOptions.onExport(filteredData, format);
    }
  };

  // Update parent component with filtered data
  useEffect(() => {
    if (onFilteredDataChange) {
      onFilteredDataChange(filteredData);
    }
  }, [filteredData, onFilteredDataChange]);

  const activeFilterCount = Object.values(activeFilters).filter(v => v !== null && v !== undefined && v !== '').length;
  const hasDateFilter = dateRange && dateRange.length === 2;
  const totalActiveFilters = activeFilterCount + (hasDateFilter ? 1 : 0) + (debouncedSearchTerm ? 1 : 0);

  return (
    <div className={`advanced-search-container ${className}`}>
      <div className="modern-search-bar">
        {/* Main Search Row */}
        <div className="d-flex gap-3 align-items-center mb-3">
          <div className="flex-grow-1">
            <Input
              size="large"
              placeholder={placeholder}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              prefix={<SearchOutlined style={{ color: '#9CA3AF' }} />}
              allowClear
              style={{
                borderRadius: '8px',
                border: '2px solid var(--gray-200)',
                fontSize: '14px'
              }}
            />
          </div>
          
          <Button
            type={showAdvanced ? "primary" : "default"}
            icon={<FilterOutlined />}
            onClick={() => setShowAdvanced(!showAdvanced)}
            style={{ borderRadius: '8px' }}
          >
            Filters {totalActiveFilters > 0 && `(${totalActiveFilters})`}
          </Button>

          {exportOptions.enabled && (
            <Button
              icon={<DownloadOutlined />}
              onClick={() => handleExport('pdf')}
              style={{ borderRadius: '8px' }}
            >
              Export
            </Button>
          )}
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="advanced-filters p-4" style={{
            background: 'var(--gray-50)',
            borderRadius: '8px',
            border: '1px solid var(--gray-200)'
          }}>
            <div className="row">
              {/* Date Range Filter */}
              <div className="col-md-4 mb-3">
                <label className="form-label text-sm font-medium text-gray-700 mb-2">
                  Date Range
                </label>
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  style={{ width: '100%', borderRadius: '6px' }}
                  placeholder={['Start Date', 'End Date']}
                />
              </div>

              {/* Dynamic Filter Fields */}
              {filterFields.map(field => (
                <div key={field.key} className="col-md-4 mb-3">
                  <label className="form-label text-sm font-medium text-gray-700 mb-2">
                    {field.label}
                  </label>
                  <Select
                    style={{ width: '100%', borderRadius: '6px' }}
                    placeholder={`Select ${field.label}`}
                    value={activeFilters[field.key]}
                    onChange={(value) => handleFilterChange(field.key, value)}
                    allowClear
                    mode={field.multiple ? "multiple" : undefined}
                  >
                    {getFilterOptions(field.key).map(option => (
                      <Option key={option} value={option}>
                        {option}
                      </Option>
                    ))}
                  </Select>
                </div>
              ))}
            </div>

            {/* Filter Actions */}
            <div className="d-flex justify-content-between align-items-center mt-3">
              <div className="d-flex gap-2 flex-wrap">
                {/* Active Filter Tags */}
                {debouncedSearchTerm && (
                  <Tag closable onClose={() => setSearchTerm('')} color="blue">
                    Search: "{debouncedSearchTerm}"
                  </Tag>
                )}
                {hasDateFilter && (
                  <Tag closable onClose={() => setDateRange(null)} color="green">
                    Date: {dateRange[0].format('MMM DD')} - {dateRange[1].format('MMM DD')}
                  </Tag>
                )}
                {Object.entries(activeFilters).map(([key, value]) => {
                  if (value !== null && value !== undefined && value !== '') {
                    const field = filterFields.find(f => f.key === key);
                    return (
                      <Tag
                        key={key}
                        closable
                        onClose={() => handleFilterChange(key, null)}
                        color="orange"
                      >
                        {field?.label}: {Array.isArray(value) ? value.join(', ') : value}
                      </Tag>
                    );
                  }
                  return null;
                })}
              </div>

              {totalActiveFilters > 0 && (
                <Button
                  type="text"
                  icon={<ClearOutlined />}
                  onClick={clearAllFilters}
                  style={{ color: 'var(--error-600)' }}
                >
                  Clear All
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Results Summary */}
        <div className="d-flex justify-content-between align-items-center mt-3">
          <span className="text-sm text-gray-600">
            Showing {filteredData.length} of {data.length} results
          </span>
          
          {/* Quick Sort Options */}
          <div className="d-flex gap-2">
            <span className="text-sm text-gray-600">Sort by:</span>
            {['date', 'customer', 'agent'].map(field => (
              <Button
                key={field}
                type="text"
                size="small"
                onClick={() => handleSort(field)}
                style={{
                  color: sortConfig.key === field ? 'var(--primary-600)' : 'var(--gray-600)',
                  fontWeight: sortConfig.key === field ? '600' : '400'
                }}
              >
                {field.charAt(0).toUpperCase() + field.slice(1)}
                {sortConfig.key === field && (
                  <span style={{ marginLeft: '4px' }}>
                    {sortConfig.direction === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSearch;
