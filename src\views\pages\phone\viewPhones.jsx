import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CFormInput,
  CPagination,CFormLabel,
  CPaginationItem,CModal,CModalHeader,CModalTitle,
  CModalBody,CModalFooter
} from '@coreui/react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import useFetch from '../../../hooks/useFetch'; 
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'


const ViewPhones = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';

  const [gName , setGName] = useState('')
  const [Evisible,setEVisible] = useState(false)
  const [GroupID,setGroupID] = useState('')
  // const [pQuantity,setPQuantity] = useState('')
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10000; 

  const { data: allGroups , loading , error} = useFetch('other/allGroups');
  const { data: singleGroup , loading : sloading , error: serror} = useFetch(GroupID ? 'other/singleGroup/' + GroupID : '');
  // console.log(singleGroup)
  const initialCashRecords = Array.isArray(allGroups)
  ? allGroups.map(group => ({
      id: group.GroupID,
      pName: group.GroupName,
      quantity: group.Quantity,
  }))
  : [];

  useEffect(() => {
    if (singleGroup) {
      setGName(singleGroup.GroupName);
      setGroupID(singleGroup.GroupID);
    }
  }, [singleGroup]);

  const filteredStaffs = initialCashRecords.filter(staff =>
    staff.pName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDelete =async (PhoneID) => {
    const response = await axios.post(`${api_path}/api/other/deleteGroup/${PhoneID}`);

    if(response.status == 200){
      toast.success("Group Deleted successfully");
      window.location.reload();
    }
  }
  const handleEdit =async (GroupID) => {
    const data = {
      gName
  }
  const response = await axios.put(`${api_path}/api/other/updateGroup/${GroupID}`,data);

  if(response.status == 200){
    toast.success("Group Updated successfully");
    setEVisible(false)
    setGName('')
    window.location.reload();
  }
  }
  // const handlePlusStock =async (PhoneID) => {
  //   // console.log(PhoneID,pQuantity)
  //   const data = {
  //       pQuantity
  //   }
  //   const response = await axios.post(`${api_path}/api/other/plusStock/${PhoneID}`,data);

  //   if(response.status == 200){
  //     toast.success("Stock Added successfully");
  //     setVisible(false)
  //     setPQuantity('')
  //     window.location.reload();
  //   }
  // }

  if (loading ) return <div>Loading...</div>;
  if (error ) return <div>Error loading</div>;
  if (sloading ) return <div>Loading...</div>;
  if (serror ) return <div>Error loading</div>;

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>All Phone </strong>
            <div className='d-flex gap-2'>
              <CFormInput
                type="text"
                placeholder="Search by Group Name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable responsive>
              <CTableHead>
                <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Group</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Quantity</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStaffs.map((group,index) => (
                  <CTableRow key={group.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>{group.pName}</CTableDataCell>
                    <CTableDataCell>{group.quantity}</CTableDataCell>

                  
                      <CTableDataCell className='d-flex gap-2'>
                        <Link
                         to={'/phone-list?groupID=' + group.id}
                         >
                        <CButton color="dark" >
                          <CIcon icon={icon.cilChevronDoubleRight} />
                        </CButton>
                        </Link>
                         {isAdmin && (
                          <>
                        <Link
                         to={'/add-phone?groupID=' + group.id}
                         >
                        <CButton color="secondary" >
                          <CIcon icon={icon.cilPlus} />
                        </CButton>
                        </Link>
                        <Link>
                          <CButton color="primary" onClick={() =>{
                              setEVisible(!Evisible);
                              setGroupID(group.id);
                          }}>
                          <CIcon icon={icon.cilPenAlt} />
                        </CButton>
                        </Link>

                        {/* <CButton 
                            color="danger" 
                            className='text-white' 
                            onClick={() => {
                              const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                              
                              if (isConfirmed) {
                                handleDelete(group.id);
                              }
                            }}

                          >                        
                          <CIcon icon={icon.cilTrash} />
                        </CButton> */}
                        </>
                        )}
                      </CTableDataCell>
                   
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>

        <CModal backdrop="static" visible={Evisible} onClose={() => setEVisible(false)}>
        <CModalHeader>
        <CModalTitle>Edit Group Name</CModalTitle>
        </CModalHeader>
        <CModalBody>
            <CRow className="mb-3">
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Name:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  value={gName}
                  onChange={(e) => setGName(e.target.value)}
                />
              </div>
            </CRow>
        </CModalBody>
        <CModalFooter>
        <CButton color="secondary" onClick={() => setEVisible(false)}>
            Close
        </CButton>
        <CButton color="primary" onClick={() => handleEdit(GroupID)}>Save changes</CButton>
        </CModalFooter>
        </CModal>
    </CRow>

  );
};

export default ViewPhones;
