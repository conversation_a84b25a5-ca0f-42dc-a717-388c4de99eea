import React, { useEffect, useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,CFormSelect,
  CFormInput,
  CFormLabel,
  CButton
} from '@coreui/react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';

import { Select } from 'antd';
import { use } from 'react';


const AddCash = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const paymentID = queryParams.get('PaymentID');
  const isEdit = id !== null;

  const navigate = useNavigate()

  const { data: cash, loading, error: cashError } = useFetch(isEdit ? 'other/singleCash/' + id : '');
  const { data: payment } = useFetch(paymentID ? 'other/singleSlow/' + paymentID : '');
  const { data: allPhone , loading : phoneLoading } = useFetch('other/allPhone');


  const [customer, setCustomer] = useState('');
  const [phone, setPhone] = useState('');
  const [PhoneID, setPhoneID] = useState('');
 const [phoneCost, setPhoneCost] = useState('');

  useEffect(() => {
    if (cash) {
      setCustomer(cash.CustomerName);
      setPhone(cash.CustomerPhoneNumber);
      setPhoneID(cash.PhoneID);
      setPhoneCost(cash.SellingPrice);
    }
  }, [cash]);
  

  const structuredPhones = allPhone?.map(phone => ({
    value: phone.PhoneID,  
    label: phone.PhoneName + ' - ' + phone.IMEI1.slice(-9),
  }));

  useEffect(() => {
    if (payment) {
      setCustomer(payment.CustomerName);
      setPhoneCost(payment.NeededAmount);
      setPhone(payment.PhoneNumber);
    }
  }, [payment]);

  const [error, setError] = useState('');

  const handleAddCash = async (e) => {
    e.preventDefault();

    setError('');

    if (!customer || !phone || !phoneCost) {
      setError('Please fill all required fields.');
      return;
    }

    const token = localStorage.getItem('joh_token');

    if (!token) {
      throw new Error('No token found');
    }

    const cashData = {
      customer,
      phone,
      PhoneID,
      phoneCost,
      paymentID
    };


    try {
      let response;

      // If we're editing, use PUT request, otherwise use POST
      if (isEdit) {
        response = await axios.put(`${api_path}/api/other/updateCash/${id}`, cashData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      } else {
        response = await axios.post(`${api_path}/api/other/addCash`, cashData, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      }

      if (response.status === 200) {
        toast.success(`${isEdit ? 'Cash Updated' : 'Cash Added'} successfully`);
        setCustomer('');
        setPhone('');
        setPhoneCost('');
        setPhoneID('');
        navigate('/view-cash');
      }

      if (response.status === 400) {
        toast.error(response.message);
        setError(response.message )
        setCustomer('');
        setPhone('');
        setPhoneCost('');
        setPhoneID('');
        navigate('/view-cash');
      }
    } catch (error) {
      console.error(error);

      if (error.response) {
        setError(error.response.data.message || 'An error occurred. Please try again.');
      } else if (error.request) {
        setError('Network error. Please check your connection.');
      } else {
        setError('An unexpected error occurred.');
      }

      toast.error(error.message || 'Failed to add cash');
    }
  };

  if (cashError) return <div>Error loading cash data: {cashError.message}</div>;
  if (loading) return <div>Loading...</div>;
  if (phoneLoading) return <div>Phone Loading...</div>;

  const handleSelectChange = (value) => {
    // console.log('Selected value:', value, options);
    setPhoneID(value);
  };

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit' : 'Add'} Cash Record</strong>
          </CCardHeader>
          <CCardBody>
            <CRow className="mb-3">
              {error && <p className="text-danger">{error}</p>}
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Jina la Mteja:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  value={customer}
                  onChange={(e) => setCustomer(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Namba ya Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="tel"
                  id="phone"
                  required
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Jina la simu:
              </CFormLabel>
              <div className="col-sm-10">
                {/* <CFormSelect aria-label="Default select example" value={PhoneID} onChange={(e) => setPhoneID(e.target.value)}>
                  <option>Open this select menu</option>
                  {allPhone.map(phone => {
                      const last6Digits = phone.IMEI1.slice(-6); 
                      return (
                          <option key={phone.PhoneID} value={phone.PhoneID}>
                              {`${phone.PhoneName} - ${last6Digits}`}
                          </option>
                      );
                  })}
                 
                </CFormSelect> */}
                    <Select
                      showSearch
                      placeholder="Select a phone"
                      style={{ width: '100%' }}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={structuredPhones}
                      onChange={handleSelectChange}
                      value={PhoneID ? { value: PhoneID } : undefined}
                    />
              </div>
            </CRow>

            
            <CRow className="mb-3">
              <CFormLabel htmlFor="bei" className="col-sm-2 col-form-label">
                Bei ya simu
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="bei"
                  value={phoneCost}
                  onChange={(e) => setPhoneCost(e.target.value)}
                />
              </div>
            </CRow>

            <CRow className="w-25 mx-auto">
              <CButton color="primary" onClick={handleAddCash}>
                {isEdit ? 'Submit' : 'Uza'}
              </CButton>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default AddCash;
