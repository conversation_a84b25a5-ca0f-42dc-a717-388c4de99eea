import React, { useRef } from "react";
import { useLocation } from "react-router-dom";
import useFetch from "../../../hooks/useFetch";
import printJS from "print-js";
// import { Watermark } from "antd";
import { MdPrint } from "react-icons/md";
import { Tooltip } from "antd";
import logo from '../../../assets/brand/joh.png';

const SalesReceipt = () => {
    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const id = queryParams.get('id');
    const type = queryParams.get('type')

    const endpoint = type == 'smart' ? 'other/singleCash/' + id : 'other/singleSmallCash/' + id;

    const { data: saleData, loading, error } = useFetch(endpoint);

    const componentRef = useRef();

    const handlePrint = () => {
        printJS({
        printable: componentRef.current,
        type: 'html',
        targetStyles: ['*'],
        });
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', {
          year: 'numeric',
          month: 'numeric',
          day: 'numeric',
        });
      };

      const formatCurrency = (value, locale = 'en-US', decimals = 0) => {
        return new Intl.NumberFormat(locale, {
          style: 'currency',
          currency: 'Tsh',
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals
        }).format(value);
      };


    if (loading) return <p>Loading...</p>;
    if (error) return <p>Error: {error}</p>;

    return (
        <div>
            <div className=" d-flex justify-content-end">

                <Tooltip title="Print Receipt">
                    <button onClick={handlePrint} className="btn btn-primary print-button">
                        <MdPrint />
                    </button>
                </Tooltip>
            </div>

        {saleData && (
            <div  class='main_container'>
            
                <div className=" mt-3 sample" ref={componentRef} >
                    
                <main className="container w-100 mx-auto" id="download-section">
                    <div className="">
                        {/* <!-- invoice Details --> */}
                        <div className="row text-center border-top mt-20 pt-20">
                            <div className="col-4 col-md-6 d-flex justify-content-end">
                                <a href="#">
                                    <img src={logo} title="invoice" alt="invoice" width={100} />
                                </a>
                            </div>
                            <div className="col-8 col-md-6 text-start mt-3">
                                <h4 className=" heading mb-6 text-title font-700 d-block"><b>JOH SHOPPING</b><br />
                                <span>PHONE & ACCESSORIES</span> <br /></h4>
                                TEL: +255 743 880 848<br />
                                Dodoma , Machinga Complex
                            </div>
                        </div>

                        <hr />
                        {/* Receipt Details */}
                
                
                        {/* <Watermark
                            height={30}
                            width={130}
                            image="https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*lkAoRbywo0oAAAAAAAAAAAAADrJ8AQ/original"
                        > */}
                            
                        <div className="row mb-3 small">
                            <div className="col-6">
                                <p className="mb-1"><strong>Date:</strong> {formatDate(saleData.muda)}</p>
                                <p className="mb-1"><strong>M/s:</strong> {saleData.CustomerName}</p>
                            </div>
                            <div className="col-6 text-end">
                                <p className="mb-1"><strong>No:</strong> <i className="dotted-underline text-primary">{saleData.code}</i></p>
                                <p className="mb-1"><strong>TIN:</strong> 154-185-011</p>
                            </div>
                        </div>
                        <hr />
                        {/* <!-- invoice descriptions --> */}
                        <div className="card mb-3 border-0">
                            <div className="card-body p-0">
                                <div className="table">
                                    <table className="table table-sm mb-3">
                                        <thead className="border-top border-bottom">
                                            <tr>
                                                <th className="py-2" style={{ width: '15%' }}>QTY</th>
                                                <th className="py-2">PARTICULARS</th>
                                                <th className="py-2 text-end">Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>1</td>
                                                <td>{saleData.PhoneName}</td>
                                                <td className="text-end">{formatCurrency(saleData.SellingPrice)}</td>
                                            </tr>
                                        </tbody>
                                        <tfoot className="border-top">
                                            <tr>
                                                <td colSpan={2} className="text-end"><strong>Total:</strong></td>
                                                <td className="text-end"><strong>{formatCurrency(saleData.SellingPrice)}</strong></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <p className="text-center">Thank you for shopping with us</p>
                        {/* </Watermark> */}

                    </div>
                </main>

                </div>
                </div>
        )}

        <style>
            {`
            .main_container {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .sample {
                width: 100%; 
                height: 100%;
                background-color:white;
            }
            .heading{
                font-size:20px
            }

            .dotted-underline {
                border-bottom: 2px dotted black; 
                padding-bottom: 1px;
            }


            @media (min-width: 768px) {
                .sample {
                    width: 40%; 
                }

                .heading span {
                    font-size:15px;
                }
            }

            `}
        </style>
        </div>
    );
};

export default SalesReceipt;