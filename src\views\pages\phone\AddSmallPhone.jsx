import React, { useEffect, useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CFormInput,
  CFormLabel,
  CButton, CFormSelect,
  CModal,CModalHeader,CModalTitle,
  CModalBody,CModalFooter
} from '@coreui/react';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation , useNavigate} from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';

import { Select } from 'antd';


const AddSmallPhone = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');

  const isEdit = id !== null;

  const navigate = useNavigate()
  const endpoint = isEdit ? 'other/singleSmallPhone/' + id  : '';

  const { data: phone, loading, error: phoneError } = useFetch(endpoint);
  
  const [submitted, setSubmitted] = useState(false);
  const [phoneName, setPhoneName] = useState('');
  const [phoneCost, setPhoneCost] = useState('');
  const [quantity, setQuantity] = useState('');
  
  useEffect(() => {
    if (phone) {
      setPhoneName(phone.PhoneName);
      setPhoneCost(phone.PhonePrice);
      setQuantity(phone.Quantity);
    }
  }, [phone]);

  
  const [error, setError] = useState('');
  
  const handleAdd = async (e) => {
    e.preventDefault();

     // Prevent double submission
     if (submitted) return;

     setSubmitted(true);

    setError('');
    
    
      if (!phoneName || !quantity || !phoneCost) {
        setError('Please fill all required fields.');
        return;
      }
      
      const token = localStorage.getItem('joh_token');

      if (!token) {
        throw new Error('No token found');
      }
      
      const phoneData = {
        phoneName,
        phoneCost,
        quantity
      };
      
      try {
        let response;
        
        // If we're editing, use PUT request, otherwise use POST
        if (isEdit) {
          response = await axios.put(`${api_path}/api/other/updateSmallPhone/${id}`, phoneData, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
        } else {
          response = await axios.post(`${api_path}/api/other/addSmallPhone`, phoneData, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
        }

        if (response.status === 200) {
          toast.success(`${isEdit ? 'Small Phone Updated' : 'Small Phone Added'} successfully`);
          setPhoneName('');
          setPhoneCost('');
          setQuantity('');
          navigate('/view-small-phones');
        }
      } catch (error) {
        console.error(error);
        
        if (error.response) {
          setError(error.response.data.message || 'An error occurred. Please try again.');
        } else if (error.request) {
          setError('Network error. Please check your connection.');
        } else {
          setError('An unexpected error occurred.');
        }
        
        toast.error(error.message || 'Failed to add phone');
      }finally {
        setSubmitted(false); 
      }
    };


  if (loading) return <div>Loading...</div>;
  if (phoneError) return <div>Error loading phone data: {cashError.message}</div>;
  
  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit' : 'Add'} Small Phone</strong>
          </CCardHeader>
          <CCardBody>
            <form action="" method="post" onSubmit={handleAdd}>
            <CRow className="mb-3">
              {error && <p className="text-danger">{error}</p>}
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Jina la Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  value={phoneName}
                  onChange={(e) => setPhoneName(e.target.value)}
                />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="bei" className="col-sm-2 col-form-label">
                Bei ya simu
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="bei"
                  value={phoneCost}
                  onChange={(e) => setPhoneCost(e.target.value)}
                />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="idadi" className="col-sm-2 col-form-label">
                Idadi
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="idadi"
                  value={quantity}
                  onChange={(e) => setQuantity(e.target.value)}
                />
              </div>
            </CRow>
             

            <CRow className="w-25 mx-auto">
              <CButton color="primary" type="submit" disabled={submitted}>
                {isEdit ? submitted ? 'Submitting' : 'Submit' : submitted ? 'Adding' : 'Add'}
              </CButton>
            </CRow>
            </form>
          </CCardBody>
        </CCard>
      </CCol>

    </CRow>
  );
};

export default AddSmallPhone;
