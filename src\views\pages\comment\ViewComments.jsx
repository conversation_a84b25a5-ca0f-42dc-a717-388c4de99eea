import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CCallout,
  CPagination,
  CPaginationItem
} from '@coreui/react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import useFetch from '../../../hooks/useFetch'; 
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
// import { useAuth } from '../../../hooks/AuthContext'

const ViewComments = () => {
//   const { role } = useAuth()

  const { data: allComments } = useFetch('other/allComments');
  const { data: notifications } = useFetch('other/allNotifications');

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', { 
      weekday: 'short',
      year: 'numeric',
      month: 'short', 
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      // second: 'numeric',
      hour12: false 
    });
  };

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardBody>
            {allComments && allComments.length > 0 ? allComments.map((comment, index) => (
              <CCallout color="info" key={index}>
                <div className="d-flex justify-content-between">
                  <div>
                    <strong>{comment.UserName}</strong> at <strong>{formatDate(comment.created_at)}</strong>
                  </div>
                  <div>
                    <button className="btn btn-success text-white" onClick={() => {
                      if (window.confirm('Are you sure?')) {
                        axios.put(`${api_path}/api/other/doneComment/${comment.CommentID}`).then(res => {
                          toast.success('Comment Done successfully');
                          window.location.reload();
                        }).catch(err => {
                          toast.error('An error occurred while processing comment');
                        });
                      }
                    }}>Done</button>
                  </div>
                </div>
                <div>{comment.Content}</div>
              </CCallout>
            )) : 
              <CCallout color="info">No comments found</CCallout>
            }
          </CCardBody>
        </CCard>


        {notifications && notifications.length > 0 && (
          <CCard className="mb-4">
            <CCardBody>

              {notifications.map((notification, index) => (
                <Link className="text-decoration-none" to={notification.type == 'slowpayment' ? '/single-payment?id=' + notification.ResourceID : ''}>
                <CCallout color="info" key={index}>
                    <div className="">
                      <div>
                        <strong>{notification.UserName}</strong> at <strong>{formatDate(notification.created_at)}</strong>
                      </div>
                    </div>
                    <div>New {notification.type == 'slowpayment' ? 'Slow Payment' : 'Notification'}</div>
                </CCallout>
                </Link>
              ))}

            </CCardBody>
          </CCard>
        )}

      </CCol>
    </CRow>
  );
};

export default ViewComments;
