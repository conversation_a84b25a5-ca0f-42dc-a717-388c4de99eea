import React from 'react';
import { Link } from 'react-router-dom';

const ModernStatsCard = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  trendValue,
  link,
  loading = false,
  className = ''
}) => {
  const colorVariants = {
    primary: {
      bg: 'var(--primary-50)',
      border: 'var(--primary-200)',
      icon: 'var(--primary-600)',
      gradient: 'linear-gradient(135deg, var(--primary-500), var(--primary-600))'
    },
    success: {
      bg: 'var(--success-50)',
      border: 'var(--success-200)',
      icon: 'var(--success-600)',
      gradient: 'linear-gradient(135deg, var(--success-500), var(--success-600))'
    },
    warning: {
      bg: 'var(--warning-50)',
      border: 'var(--warning-200)',
      icon: 'var(--warning-600)',
      gradient: 'linear-gradient(135deg, var(--warning-500), var(--warning-600))'
    },
    error: {
      bg: 'var(--error-50)',
      border: 'var(--error-200)',
      icon: 'var(--error-600)',
      gradient: 'linear-gradient(135deg, var(--error-500), var(--error-600))'
    },
    info: {
      bg: 'var(--info-50)',
      border: 'var(--info-200)',
      icon: 'var(--info-600)',
      gradient: 'linear-gradient(135deg, var(--info-500), var(--info-600))'
    },
    gray: {
      bg: 'var(--gray-50)',
      border: 'var(--gray-200)',
      icon: 'var(--gray-600)',
      gradient: 'linear-gradient(135deg, var(--gray-500), var(--gray-600))'
    }
  };

  const currentColor = colorVariants[color] || colorVariants.primary;

  const getTrendIcon = () => {
    if (!trend) return null;
    
    if (trend === 'up') {
      return <span style={{ color: 'var(--success-600)' }}>↗️</span>;
    } else if (trend === 'down') {
      return <span style={{ color: 'var(--error-600)' }}>↘️</span>;
    } else {
      return <span style={{ color: 'var(--gray-500)' }}>➡️</span>;
    }
  };

  const CardContent = () => (
    <div 
      className={`modern-stats-card ${className}`}
      style={{
        background: 'white',
        borderRadius: 'var(--radius-xl)',
        padding: 'var(--space-6)',
        boxShadow: 'var(--shadow-sm)',
        border: '1px solid var(--gray-200)',
        transition: 'all var(--transition-normal)',
        position: 'relative',
        overflow: 'hidden',
        height: '100%'
      }}
    >
      {/* Top accent line */}
      <div 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: currentColor.gradient
        }}
      />

      <div className="d-flex justify-content-between align-items-start mb-4">
        <div className="flex-grow-1">
          <h6 
            className="stats-title mb-2"
            style={{
              fontSize: 'var(--text-sm)',
              fontWeight: '500',
              color: 'var(--gray-600)',
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              margin: 0
            }}
          >
            {title}
          </h6>
          
          {loading ? (
            <div className="skeleton-loader" style={{ height: '32px', width: '80px' }} />
          ) : (
            <div 
              className="stats-value"
              style={{
                fontSize: 'var(--text-3xl)',
                fontWeight: '700',
                color: 'var(--gray-900)',
                lineHeight: '1.2',
                fontFamily: 'var(--font-family-heading)'
              }}
            >
              {value}
            </div>
          )}
        </div>

        {icon && (
          <div 
            className="stats-icon"
            style={{
              width: '48px',
              height: '48px',
              borderRadius: '50%',
              background: currentColor.gradient,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '20px',
              flexShrink: 0
            }}
          >
            {icon}
          </div>
        )}
      </div>

      <div className="d-flex justify-content-between align-items-center">
        <div className="flex-grow-1">
          {subtitle && (
            <p 
              className="stats-subtitle mb-0"
              style={{
                fontSize: 'var(--text-sm)',
                color: 'var(--gray-500)',
                margin: 0
              }}
            >
              {subtitle}
            </p>
          )}
        </div>

        {(trend || trendValue) && (
          <div 
            className="stats-trend"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--space-1)',
              fontSize: 'var(--text-sm)',
              fontWeight: '500'
            }}
          >
            {getTrendIcon()}
            {trendValue && (
              <span style={{
                color: trend === 'up' ? 'var(--success-600)' : 
                       trend === 'down' ? 'var(--error-600)' : 'var(--gray-500)'
              }}>
                {trendValue}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Hover effect overlay */}
      <div 
        className="hover-overlay"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: currentColor.gradient,
          opacity: 0,
          transition: 'opacity var(--transition-fast)',
          pointerEvents: 'none'
        }}
      />
    </div>
  );

  if (link) {
    return (
      <Link 
        to={link} 
        className="text-decoration-none"
        style={{
          display: 'block',
          height: '100%'
        }}
        onMouseEnter={(e) => {
          const overlay = e.currentTarget.querySelector('.hover-overlay');
          if (overlay) overlay.style.opacity = '0.05';
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = 'var(--shadow-lg)';
        }}
        onMouseLeave={(e) => {
          const overlay = e.currentTarget.querySelector('.hover-overlay');
          if (overlay) overlay.style.opacity = '0';
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = 'var(--shadow-sm)';
        }}
      >
        <CardContent />
      </Link>
    );
  }

  return <CardContent />;
};

export default ModernStatsCard;
