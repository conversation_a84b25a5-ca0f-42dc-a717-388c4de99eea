import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { api_path } from '../../environments';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [role,setRole] = useState(null);
    const [loading, setLoading] = useState(true);

    // Fetching user data from the backend using the token
    const fetchUserData = async (token) => {
        try {
            const response = await axios.get(api_path + '/api/auth/getLoggedInUser', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });
    
            setUser(response.data.user); 
            setRole(response.data.user.RoleName); 
            
        } catch (error) {
            console.error('Error fetching user data:', error);
            alert('try login again');
            setUser(null);
            setRole(null);
        } finally {
            setLoading(false);
        }
    };
    
    useEffect(() => {
        const token = localStorage.getItem('joh_token');
        if (token) {
            fetchUserData(token); 
        } else {
            setLoading(false);
        }
    }, []);

    const login = (token) => {
        localStorage.setItem('joh_token', token);
        fetchUserData(token); 
    };

    const logout = () => {
        setUser(null);
        setRole(null)
        localStorage.removeItem('joh_token'); 
        window.location.href = '/';
    };

    return (
        <AuthContext.Provider value={{ user,role, login, logout, loading }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    return useContext(AuthContext);
};
