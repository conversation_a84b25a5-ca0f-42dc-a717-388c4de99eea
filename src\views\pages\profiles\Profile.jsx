import React,{useState} from 'react'
import {
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CForm,
  CFormInput,
  CFormLabel,
  CModal, 
  CModalBody,
  CSpinner,
  CModalHeader,
  CModalTitle
  } from '@coreui/react'
  import { useAuth } from '../../../hooks/AuthContext'
import axios from 'axios'
import { toast } from 'react-toastify';
import { api_path } from '../../../../environments';



const Profile = () => {
  const { user , role } = useAuth()

  const [name,setName] = useState(user ? user.UserName : '')
  const [email,setEmail] = useState(user ? user.Email : '')
  const [number,setNumber] = useState(user ? user.PhoneNumber : '')
  const [visible, setVisible] = useState(false)

  const [loading , setLoading] = useState(false)

  const [oldPassword,setOldPassword] = useState('')
  const [newPassword,setNewPassword] = useState('')
  const [repeatPassword,setRepeatPassword] = useState('')
  const [p_error,setP_Error] = useState('')

  const handleUpdate = async (e) => {
     e.preventDefault()
     setLoading(true)
     
      const profileData = {
          name : name,
          email : email ,
          phone : number
      }
     
       const response = await axios.put(`${api_path}/api/auth/editProfile/${user.UserID}`, profileData)

        if(response.status == 200){

          setInterval(() => {
            setLoading(false)     
          }, 1000);

          toast.success(response.data.message);

        }else{
          toast.error('error')
        }

  }

  const handleChangePassword = async (e) => {
    e.preventDefault();

    const token = localStorage.getItem('joh_token');

    const pwds = {
        currentPassword: oldPassword,  
        newPassword: newPassword,
        confirmNewPassword: repeatPassword
    }
        try {
          if(newPassword != repeatPassword){
            setP_Error('Password not match!')
          } else{

            const response = await axios.put(
                `${api_path}/api/auth/changePwd`,pwds,
                {
                    headers: {
                        Authorization: `Bearer ${token}`
                    }
                }
            );

            
            if (response.status == 200) {
                toast.success("Password changed successfully");
                setOldPassword('')
                setNewPassword('')
                setRepeatPassword(' ')
                setVisible(false);
            }else{
              setP_Error(response.message);
            }
          }
        } catch (error) {
          if (error.response) {
            setP_Error(error.response.data.message || "An error occurred");
        } else {
            setP_Error("An unexpected error occurred");
        }
        }
};


  
  return (
    <>
      <div className='container'>
      <CCol xs={12}>
        <CCard className="mb-4">
            <CCardHeader>
              My Profile
            </CCardHeader>
          <CCardBody>
            
            <p className="text-body-secondary small">
              This is your profile
            </p>
              <CForm className="row g-3" onSubmit={handleUpdate}>
                <CCol md={6}>
                  <CFormLabel htmlFor="inputName">Name</CFormLabel>
                  <CFormInput type="text" id="inputName" value={name} onChange={(e) => setName(e.target.value)}/>
                </CCol>
                <CCol md={6}>
                  <CFormLabel htmlFor="inputEmail4">Email</CFormLabel>
                  <CFormInput type="email" id="inputEmail4" value={email} onChange={(e) => setEmail(e.target.value)} required/>
                </CCol>
                <CCol xs={12}>
                  <CFormLabel htmlFor="inputNumber">Phone Number</CFormLabel>
                  <CFormInput id="inputNumber" placeholder="" value={number} onChange={(e) => setNumber(e.target.value)}/>
                </CCol>
              
                <CCol xs={6}>
                  
                  <CButton color="secondary" onClick={() => setVisible(!visible)}>
                    Change Password
                  </CButton>
                </CCol>

                <CCol xs={12}>
                  {loading ? (
                    <CButton color="primary" disabled>
                      <CSpinner as="span" size="sm" aria-hidden="true" />
                      Loading...
                    </CButton>
                  ) : (

                    <CButton color="primary" type="submit">
                      Save
                    </CButton>
                  )}
                </CCol>
              </CForm>
          </CCardBody>
        </CCard>
      </CCol>

      <CModal
      visible={visible}
      onClose={() => setVisible(false)}
      aria-labelledby="LiveDemoExampleLabel"
    >
      <CModalHeader>
        <CModalTitle id="LiveDemoExampleLabel">Change Password</CModalTitle>
      </CModalHeader>
      <CModalBody>
      {p_error && (<p className='text-danger'>{p_error}</p>)}
      <CForm className="row g-3" onSubmit={handleChangePassword}>
                <CCol md={12}>
                  <CFormLabel htmlFor="inputold">Current Password</CFormLabel>
                  <CFormInput type="password" id="inputold" value={oldPassword} onChange={(e) => setOldPassword(e.target.value)}/>
                </CCol>
                <CCol md={12}>
                  <CFormLabel htmlFor="inputnew">New Password</CFormLabel>
                  <CFormInput type="password" id="inputnew" value={newPassword} onChange={(e) => setNewPassword(e.target.value)}/>
                </CCol>
                <CCol md={12}>
                  <CFormLabel htmlFor="inputrepeat">Repeat Password</CFormLabel>
                  <CFormInput type="password" id="inputrepeat" value={repeatPassword} onChange={(e) => setRepeatPassword(e.target.value)}/>
                </CCol>
                <CCol md='6'></CCol>
                <CCol md='6'>

                  <CButton color="primary" type='submit'>Save changes</CButton>
                  {' '}
                  <CButton color="secondary" onClick={() => setVisible(false)}>
                    Close
                  </CButton>
                </CCol>
              </CForm>
      </CModalBody>
      {/* <CModalFooter>
        
      </CModalFooter> */}
    </CModal>
      </div>
    </>
  )
}

export default Profile
