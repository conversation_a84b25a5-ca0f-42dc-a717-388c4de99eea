import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CFormInput,
  CPagination,
  CPaginationItem,CProgress,
  CAccordion, CAccordionBody, CAccordionHeader, CAccordionItem ,
  CModal,CModalHeader,CModalTitle,
  CModalBody,CModalFooter,CFormLabel
} from '@coreui/react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import useFetch from '../../../hooks/useFetch'; 
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'
import { useLocation } from 'react-router-dom';

const SinglePayment = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');

  const [visible, setVisible] = useState(false);
  const [nAmount,setNAmount] = useState('')
  const [progressID,setProgressID] = useState('')
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10000; 

  const { data: allPaymentItems , loading , error} = useFetch(id ? 'other/allPaymentItems/' + id : '');


  const initialCashRecords = Array.isArray(allPaymentItems)
  ? allPaymentItems.map(payment => ({
      id: payment.ProgressID,
      day: new Date(payment.created_at),
      agent: payment.UserName,
      amount: payment.AmountPaid,
  }))
  : [];



  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'Tsh' }).format(value);
  }

  const paidPerCent = (paid,needed) => {
    return Math.round((paid / needed) * 100);
  }

  const filteredStaffs = initialCashRecords.filter(staff =>
    staff.day.toLocaleDateString().includes(searchTerm.toLowerCase()) ||
    staff.amount.includes(searchTerm)
  );

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDeleteCash =async (paymentID) => {
    const response = await axios.post(`${api_path}/api/other/deleteSlow/${paymentID}`);

    if(response.status == 200){
      toast.success("Record Deleted successfully");
      window.location.reload();
    }
  }
  const handleEditPayment =async (progressID) => {
    const response = await axios.put(`${api_path}/api/other/editProgress/${progressID}`,{
      amount: nAmount
    }
    );

    if(response.status == 200){
      toast.success("Record Updated successfully");
      window.location.reload();
    }
  }

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading users: {error.message}</div>;

  return (
    <>
    <CRow>
      <CProgress className='mb-3' 
        variant="striped" animated
        value={paidPerCent(allPaymentItems[0].TotalAmountPaid,allPaymentItems[0].NeededAmount)}
        color={paidPerCent(allPaymentItems[0].TotalAmountPaid, allPaymentItems[0].NeededAmount) === 100 ? 'success' : 'primary'}>
          {paidPerCent(allPaymentItems[0].TotalAmountPaid,allPaymentItems[0].NeededAmount)}%
        </CProgress>
    </CRow>
    <CRow>
    <CAccordion className='mb-3'>
      <CAccordionItem itemKey={1}>
        <CAccordionHeader>Maelezo Kamili</CAccordionHeader>
        <CAccordionBody>
        <CRow>
            {/* Left Column (Return Titles) */}
            <CCol md={6}>
              <p><strong>Customer Name:</strong> &nbsp; {allPaymentItems[0].CustomerName}</p>
              <p><strong>Phone Number:</strong> &nbsp; <a href={`tel:${allPaymentItems[0].PhoneNumber}`}>{allPaymentItems[0].PhoneNumber}</a></p>
            </CCol>

            {/* Right Column (Return Data) */}
            <CCol md={6}>
              <p><strong>Jina La simu :</strong> &nbsp; {allPaymentItems[0].NeededPhone}</p>
              <p><strong>Inayohitajika:</strong> &nbsp; {formatCurrency(allPaymentItems[0].NeededAmount)}</p>
            </CCol>
          </CRow>
        </CAccordionBody>
      </CAccordionItem>
    </CAccordion>
    </CRow>
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>All Slow Payments</strong>
            <div className='d-flex gap-2'>
              <CFormInput
                type="text"
                placeholder="Search by Day"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable responsive>
              <CTableHead>
                <CTableRow>
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Day</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Amount</CTableHeaderCell>
                  {isAdmin && (
                    <>
                    <CTableHeaderCell scope="col">Agent</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                    </>
                  )}
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStaffs.map((cash,index) => (
                  <CTableRow key={cash.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>
                      {cash.day.toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                      })}
                    </CTableDataCell>
                    <CTableDataCell>{formatCurrency(cash.amount)}</CTableDataCell>

                    {isAdmin && (
                      <>
                      <CTableDataCell>{cash.agent}</CTableDataCell>

                      <CTableDataCell className='d-flex gap-2'>
                        {/* <Link to={'/single-record?type=cash&id=' + cash.id}>
                        <CButton color="secondary">
                          <CIcon icon={icon.cilChevronDoubleRight} />
                        </CButton>
                        </Link> */}
                        <CButton color="primary" onClick={() => {
                          setVisible(!visible);
                          setProgressID(cash.id);
                          setNAmount(cash.amount);
                        }}>
                          <CIcon icon={icon.cilPenAlt} />
                        </CButton>
                        <CButton 
                            color="danger" 
                            className='text-white' 
                            onClick={() => {
                              const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                              
                              if (isConfirmed) {
                                handleDeleteCash(cash.id);
                              }
                            }}

                          >                        
                          <CIcon icon={icon.cilTrash} />
                        </CButton>
                      </CTableDataCell>
                    </>
                    )}
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>

      <CModal backdrop="static" visible={visible} onClose={() => setVisible(false)}>
        <CModalHeader>
        <CModalTitle>Edit Payment</CModalTitle>
        </CModalHeader>
        <CModalBody>
            <CRow className="mb-3">
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Kiasi:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="name"
                  value={nAmount}
                  onChange={(e) => setNAmount(e.target.value)}
                />
              </div>
            </CRow>
        </CModalBody>
        <CModalFooter>
        <CButton color="secondary" onClick={() => setEVisible(false)}>
            Close
        </CButton>
        <CButton color="primary" 
        onClick={() => handleEditPayment(progressID)}
        >Save changes</CButton>
        </CModalFooter>
        </CModal>
    </CRow>
    </>
  );
};

export default SinglePayment;
