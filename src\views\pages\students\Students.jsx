import React, { useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CAvatar,
  CButton,
  CFormInput,
  CPagination,
  CPaginationItem,
} from '@coreui/react';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import avatar from '../../../assets/images/avatars/2.jpg';
import useFetch from '../../../hooks/useFetch';

const Students = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8; 

  const { data: students, loading, error } = useFetch('students');

  const initialStudents = students ? students.map((student) => ({
    id: student.studentID,
    reg_no: student.registrationNumber,
    name: student.firstName + ' ' + student.middleName + ' ' + student.lastName,
    email: student.email,
    phone: student.phone,
    // hasProfile: !!student.imageURL, 
    hasProfile: false, 
  })) : [];

  const filteredStudents = initialStudents.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.reg_no.includes(searchTerm) ||
    student.phone.includes(searchTerm)
  );

  const totalPages = Math.ceil(filteredStudents.length / itemsPerPage);
  
  const indexOfLastStudent = currentPage * itemsPerPage;
  const indexOfFirstStudent = indexOfLastStudent - itemsPerPage;
  const currentStudents = filteredStudents.slice(indexOfFirstStudent, indexOfLastStudent);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading students: {error.message}</div>;

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>All Students</strong>
            <CFormInput
              type="text"
              placeholder="Search by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mt-2"
            />
          </CCardHeader>
          <CCardBody>
            <CTable>
              <CTableHead>
                <CTableRow>
                  <CTableHeaderCell scope="col">#</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Profile</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Registration</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Name</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Email</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Phone</CTableHeaderCell>
                  <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStudents.map((student) => (
                  <CTableRow key={student.id}>
                    <CTableHeaderCell scope="row">{student.id}</CTableHeaderCell>
                    <CTableDataCell>
                      {student.hasProfile ? (
                        <CAvatar src={avatar} />
                      ) : (
                        <CAvatar color="secondary">{student.name.substring(0, 2).toUpperCase()}</CAvatar>
                      )}
                    </CTableDataCell>
                    <CTableDataCell>{student.reg_no}</CTableDataCell>
                    <CTableDataCell>{student.name}</CTableDataCell>
                    <CTableDataCell>{student.email}</CTableDataCell>
                    <CTableDataCell>{student.phone}</CTableDataCell>
                    <CTableDataCell className='d-flex gap-2'>
                      <CButton color="secondary">
                        <CIcon icon={icon.cilOptions} />
                      </CButton>
                      <CButton color="primary">
                        <CIcon icon={icon.cilPenAlt} />
                      </CButton>
                      <CButton color="danger" className='text-white'>
                        <CIcon icon={icon.cilTrash} />
                      </CButton>
                    </CTableDataCell>
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default Students;
