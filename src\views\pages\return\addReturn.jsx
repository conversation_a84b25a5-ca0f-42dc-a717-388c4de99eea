import React, { useEffect, useState } from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CFormInput,
  CFormLabel,
  CButton,
} from '@coreui/react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation, useNavigate } from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';
import { Select } from 'antd';

const AddReturn = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const isEdit = id !== null;

  const navigate = useNavigate();

  const { data: returns, loading, error: cashError } = useFetch(isEdit ? 'other/singleReturn/' + id : '');
  const { data: allPhone, loading: phoneLoading } = useFetch('other/allPhone');
  const { data: allSoldPhone, loading: sphoneLoading } = useFetch('other/soldPhones');

  const [customer, setCustomer] = useState('');
  const [phone, setPhone] = useState('');
  const [RPPhoneID, setRPPhoneID] = useState('');
  const [TPPhoneID, setTPPhoneID] = useState('');
  const [comment, setComment] = useState('');

  useEffect(() => {
    if (returns) {
      setCustomer(returns.CustomerName);
      setPhone(returns.PhoneNumber);
      setRPPhoneID(returns.RPPhoneID);
      setTPPhoneID(returns.TPPhoneID);
      setComment(returns.comment);
    }
  }, [returns]);

  const structuredPhones = allPhone?.map(phone => ({
    value: phone.PhoneID,
    label: phone.PhoneName + ' - ' + phone.IMEI1.slice(-9),
  }));

  const structuredRPPhones = allSoldPhone?.map(phone => ({
    value: phone.PhoneID,
    label: phone.PhoneName + ' - ' + phone.IMEI1.slice(-9),
  }));

  const [error, setError] = useState('');

  const handleAddCash = async (e) => {
    e.preventDefault();

    setError('');

    if (!customer || !phone || !comment) {
      setError('Please fill all required fields.');
      return;
    }

    const token = localStorage.getItem('joh_token');

    if (!token) {
      throw new Error('No token found');
    }

    const returnData = {
      customer,
      phone,
      RPPhoneID,
      TPPhoneID,
      comment,
    };

    try {
      let response;

      if (isEdit) {
        response = await axios.put(`${api_path}/api/other/updateReturn/${id}`, returnData, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      } else {
        response = await axios.post(`${api_path}/api/other/addReturn`, returnData, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
      }

      if (response.status === 200) {
        toast.success(`${isEdit ? 'return Updated' : 'return Added'} successfully`);
        setCustomer('');
        setPhone('');
        setRPPhoneID('');
        setTPPhoneID('');
        setComment('');
        navigate('/view-returns');
      }
    } catch (error) {
      console.error(error);

      if (error.response) {
        setError(error.response.data.message || 'An error occurred. Please try again.');
      } else if (error.request) {
        setError('Network error. Please check your connection.');
      } else {
        setError('An unexpected error occurred.');
      }

      toast.error(error.message || 'Failed to add return');
    }
  };

  if (loading) return <div>Loading...</div>;
  if (cashError) return <div>Error loading cash data: {cashError.message}</div>;
  if (phoneLoading) return <div>Phone Loading...</div>;
  if (sphoneLoading) return <div>Phone Loading...</div>;

  const handleSelectChange = (value) => {
    setTPPhoneID(value);
  };

  const handleSelectRPChange = (value) => {
    setRPPhoneID(value);
  };

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit' : 'Add'} Return Record</strong>
          </CCardHeader>
          <CCardBody>
            <CRow className="mb-3">
              {error && <p className="text-danger">{error}</p>}
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Jina la Mteja:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="name"
                  value={customer}
                  onChange={(e) => setCustomer(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Namba ya Simu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="tel"
                  id="phone"
                  required
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </div>
            </CRow>
            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Simu Iliyorudishwa:
              </CFormLabel>
              <div className="col-sm-10">
                <Select
                  showSearch
                  placeholder="Select a phone"
                  style={{ width: '100%' }}
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={structuredRPPhones}
                  onChange={handleSelectRPChange}
                  value={RPPhoneID}
                />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
                Simu Aliyochukua:
              </CFormLabel>
              <div className="col-sm-10">
                <Select
                  showSearch
                  placeholder="Select a phone"
                  style={{ width: '100%' }}
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  options={structuredPhones}
                  onChange={handleSelectChange}
                  value={TPPhoneID}
                />
              </div>
            </CRow>

            <CRow className="mb-3">
              <CFormLabel htmlFor="comment" className="col-sm-2 col-form-label">
                Sababu:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="text"
                  id="comment"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                />
              </div>
            </CRow>

            <CRow className="w-25 mx-auto">
              <CButton color="primary" onClick={handleAddCash}>
                {isEdit ? 'Submit' : 'Badilisha'}
              </CButton>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default AddReturn;
