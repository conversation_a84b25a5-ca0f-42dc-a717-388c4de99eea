import React, { useState , useEffect} from 'react';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CFormInput,
  CFormLabel,
  CButton
} from '@coreui/react';
import axios from 'axios';
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import { useLocation
   , useNavigate
  } from 'react-router-dom';
import useFetch from '../../../hooks/useFetch';

const AddStaff = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const id = queryParams.get('id');
  const isEdit = id !== null;

  const navigate = useNavigate()

  const { data: user, loading, error: userError } = useFetch(isEdit ? 'auth/singleUser/' + id : '');


  const [fname , setFname] = useState('')
  const [uname , setUname] = useState('')
  const [email , setEmail] = useState('')
  const [password , setPassword] = useState('')
  const [phone , setPhone] = useState('')
  
  const [error , setError] = useState('')

  useEffect(() => {
    if (user) {
      setFname(user.FirstName)
      setUname(user.UserName)
      setEmail(user.Email)
      setPhone(user.PhoneNumber)
      setPassword(user.Password)
    }
  }, [user]);

  const handleAddUser = async (e) => {
    e.preventDefault();
  
    if (!fname || !uname || !email || !password || !phone) {
      setError("Please fill all fields.");
      return;
    }
  
    if (!/\S+@\S+\.\S+/.test(email)) {
      setError("Please enter a valid email.");
      return;
    }
  
    if (phone.length < 10) {
      setError("Phone number should have at least 10 digits.");
      return;
    }
  
    const userData = {
      firstname: fname,
      username: uname,
      email: email,
      password: password,
      phone: phone
    };
  
    try {
      let response;
      if (isEdit) {
        response = await axios.put(`${api_path}/api/auth/updateUser/${id}`, userData);
        if (response.status === 200) {
          toast.success("User updated successfully");
          navigate('/view-users');
        }
      } else {
        response = await axios.post(`${api_path}/api/auth/addUser`, userData);
        if (response.status === 200) {
          toast.success("User added successfully");
          navigate('/view-users');        }
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || "An error occurred";
      setError(errorMessage);
    }
  };
  

  
  if (loading) return <div>Loading...</div>;
  if (userError) return <div>Error loading cash data: {userError.message}</div>;

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>{isEdit ? 'Edit': 'Add'} User</strong>
          </CCardHeader>
          <CCardBody>

          <CRow className="mb-3">
            {error && (
              <p className='text-danger'>{error}</p>
            )}
            <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
              Firstname :
            </CFormLabel>
            <div className="col-sm-10">
              <CFormInput
                type="text"
                id="name"
                value={fname}
                onChange={(e) => setFname(e.target.value)}
              />
            </div>
          </CRow>
          <CRow className="mb-3">
            <CFormLabel htmlFor="uname" className="col-sm-2 col-form-label">
              Username :
            </CFormLabel>
            <div className="col-sm-10">
              <CFormInput
                type="text"
                id="uname" required
                value={uname}
                onChange={(e) => setUname(e.target.value)}
              />
            </div>
          </CRow>
          <CRow className="mb-3">
            <CFormLabel htmlFor="email" className="col-sm-2 col-form-label">
              Email :
            </CFormLabel>
            <div className="col-sm-10">
              <CFormInput
                type="text"
                id="email" required
                placeholder="<EMAIL>"
                
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          </CRow>
          <CRow className="mb-3">
            <CFormLabel htmlFor="phone" className="col-sm-2 col-form-label">
              Phone :
            </CFormLabel>
            <div className="col-sm-10">
              <CFormInput
                type="text"
                id="phone" required                
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
              />
            </div>
          </CRow>
          <CRow className="mb-3">
            <CFormLabel htmlFor="inputPassword" className="col-sm-2 col-form-label">
              Password :
            </CFormLabel>
            <div className="col-sm-10">
              <CFormInput type={isEdit ? "text" :"password"} id="inputPassword"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)} />
            </div>
          </CRow>

          <CRow className='w-25 mx-auto' onClick={handleAddUser}>
            <CButton color='primary'>{isEdit ? 'Update': 'Add'}</CButton>
          </CRow>

          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default AddStaff;
