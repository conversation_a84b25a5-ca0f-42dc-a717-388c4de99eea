import React, { useEffect, useRef } from 'react'
import PropTypes from 'prop-types'

import {
  CRow,
  CCol,
  CDropdown,
  CDropdownMenu,
  CDropdownItem,
  CDropdownToggle,
  CWidgetStatsA,
} from '@coreui/react'
import { getStyle } from '@coreui/utils'
import { CChartBar, CChartLine } from '@coreui/react-chartjs'
import CIcon from '@coreui/icons-react'
import { cilArrowBottom, cilArrowTop, cilOptions } from '@coreui/icons'
import useFetch from '../../hooks/useFetch';
import { Link } from 'react-router-dom'
import ModernStatsCard from '../../components/ModernStatsCard'
import { useAuth } from '../../hooks/AuthContext'


const WidgetsDropdown = (props) => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';
  

  const widgetChartRef1 = useRef(null)
  const widgetChartRef2 = useRef(null)

  const { data : phoneCount } = useFetch('other/phoneCount');
  const { data : saleCount } = useFetch('other/saleCount');
  const { data : SmallSaleCount } = useFetch('other/SmallSaleCount');
  const { data : returnCount } = useFetch('other/returnCount');
  const { data : userCount } = useFetch('auth/userCount');
  const { data : smallphoneCount } = useFetch('other/SmallPhoneCount');
  const { data : userphoneCount } = useFetch('other/userPhoneCount');

  console.log(returnCount)
  useEffect(() => {
    document.documentElement.addEventListener('ColorSchemeChange', () => {
      if (widgetChartRef1.current) {
        setTimeout(() => {
          widgetChartRef1.current.data.datasets[0].pointBackgroundColor = getStyle('--cui-primary')
          widgetChartRef1.current.update()
        })
      }

      if (widgetChartRef2.current) {
        setTimeout(() => {
          widgetChartRef2.current.data.datasets[0].pointBackgroundColor = getStyle('--cui-info')
          widgetChartRef2.current.update()
        })
      }
    })
  }, [widgetChartRef1, widgetChartRef2])

  return (
    <CRow className={props.className} xs={{ gutter: 4 }}>
      <CCol sm={6} xl={4} xxl={3}>
        <ModernStatsCard
          title="Smart Phones"
          value={isAdmin ? phoneCount : userphoneCount}
          subtitle="Total devices in inventory"
          icon="📱"
          color="primary"
          link="/view-phones"
          loading={phoneLoading}
          trend="up"
          trendValue="+12%"
        />
      </CCol>
      {isAdmin && (
        <CCol sm={6} xl={4} xxl={3}>
          <ModernStatsCard
            title="Simu Ndogo"
            value={smallphoneCount}
            subtitle="Basic phones in stock"
            icon="📞"
            color="gray"
            link="/view-small-phones"
            loading={phoneLoading}
            trend="stable"
            trendValue="0%"
          />
        </CCol>
      )}

      <CCol sm={6} xl={4} xxl={3}>
        <ModernStatsCard
          title="Smart Phone Sales"
          value={saleCount}
          subtitle="Total transactions completed"
          icon="💰"
          color="success"
          link="/view-cash"
          loading={saleLoading}
          trend="up"
          trendValue="+8%"
        />
      </CCol>

      <CCol sm={6} xl={4} xxl={3}>
        <ModernStatsCard
          title="Basic Phone Sales"
          value={SmallSaleCount}
          subtitle="Simu ndogo transactions"
          icon="📊"
          color="info"
          link="/view-cash-small"
          loading={saleLoading}
          trend="up"
          trendValue="+5%"
        />
      </CCol>

      <CCol sm={6} xl={4} xxl={3}>
        <ModernStatsCard
          title="Returns"
          value={returnCount}
          subtitle="Items returned this month"
          icon="↩️"
          color="warning"
          link="/view-returns"
          loading={returnLoading}
          trend="down"
          trendValue="-3%"
        />
      </CCol>

      {isAdmin && (
        <CCol sm={6} xl={4} xxl={3}>
          <ModernStatsCard
            title="System Users"
            value={userCount}
            subtitle="Active user accounts"
            icon="👥"
            color="error"
            link="/view-users"
            loading={userLoading}
            trend="stable"
            trendValue="0%"
          />
        </CCol>
      )}
    </CRow>
  )
}

WidgetsDropdown.propTypes = {
  className: PropTypes.string,
  withCharts: PropTypes.bool,
}

export default WidgetsDropdown
