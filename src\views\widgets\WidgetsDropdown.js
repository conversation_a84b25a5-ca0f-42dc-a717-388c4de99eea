import React, { useEffect, useRef } from 'react'
import PropTypes from 'prop-types'

import {
  CRow,
  CCol,
  CDropdown,
  CDropdownMenu,
  CDropdownItem,
  CDropdownToggle,
  CWidgetStatsA,
} from '@coreui/react'
import { getStyle } from '@coreui/utils'
import { CChartBar, CChartLine } from '@coreui/react-chartjs'
import CIcon from '@coreui/icons-react'
import { cilArrowBottom, cilArrowTop, cilOptions } from '@coreui/icons'
import useFetch from '../../hooks/useFetch'; 
import { Link } from 'react-router-dom'
import { useAuth } from '../../hooks/AuthContext'


const WidgetsDropdown = (props) => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';
  

  const widgetChartRef1 = useRef(null)
  const widgetChartRef2 = useRef(null)

  const { data : phoneCount } = useFetch('other/phoneCount');
  const { data : saleCount } = useFetch('other/saleCount');
  const { data : SmallSaleCount } = useFetch('other/SmallSaleCount');
  const { data : returnCount } = useFetch('other/returnCount');
  const { data : userCount } = useFetch('auth/userCount');
  const { data : smallphoneCount } = useFetch('other/SmallPhoneCount');
  const { data : userphoneCount } = useFetch('other/userPhoneCount');

  console.log(returnCount)
  useEffect(() => {
    document.documentElement.addEventListener('ColorSchemeChange', () => {
      if (widgetChartRef1.current) {
        setTimeout(() => {
          widgetChartRef1.current.data.datasets[0].pointBackgroundColor = getStyle('--cui-primary')
          widgetChartRef1.current.update()
        })
      }

      if (widgetChartRef2.current) {
        setTimeout(() => {
          widgetChartRef2.current.data.datasets[0].pointBackgroundColor = getStyle('--cui-info')
          widgetChartRef2.current.update()
        })
      }
    })
  }, [widgetChartRef1, widgetChartRef2])

  return (
    <CRow className={props.className} xs={{ gutter: 4 }}>
      <CCol sm={6} xl={4} xxl={3}>
        <Link to={'/view-phones'} style={{ textDecoration: 'none' }}>
        <div className="dashboard-card">
          <div className="dashboard-card-title">Smart Phones</div>
          <div className="dashboard-card-value">
            {isAdmin ? phoneCount : userphoneCount}
          </div>
          <div className="d-flex align-items-center justify-content-between">
            <span className="text-gray-500" style={{ fontSize: '0.875rem' }}>
              Total devices
            </span>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, var(--primary-500), var(--primary-600))',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              📱
            </div>
          </div>
        </div>
        {/* <CWidgetStatsA
          color="primary"
          value={
            <>
              {isAdmin ? phoneCount : userphoneCount}{' '}
            </>
          }
          title="Smart Phones"
          // action={
          //   <CDropdown alignment="end">
          //     <CDropdownToggle color="transparent" caret={false} className="text-white p-0">
          //       <CIcon icon={cilOptions} />
          //     </CDropdownToggle>
          //     <CDropdownMenu>
          //       <CDropdownItem>Action</CDropdownItem>
          //       <CDropdownItem>Another action</CDropdownItem>
          //       <CDropdownItem>Something else here...</CDropdownItem>
          //       <CDropdownItem disabled>Disabled action</CDropdownItem>
          //     </CDropdownMenu>
          //   </CDropdown>
          // }
          chart={
            <CChartLine
              ref={widgetChartRef1}
              className="mt-3 mx-3"
              style={{ height: '70px' }}
              data={{
                labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
                datasets: [
                  {
                    label: 'My First dataset',
                    backgroundColor: 'transparent',
                    borderColor: 'rgba(255,255,255,.55)',
                    pointBackgroundColor: getStyle('--cui-primary'),
                    data: [0, 0, 0, 0, 0, 0, 0],
                  },
                ],
              }}
              options={{
                plugins: {
                  legend: {
                    display: false,
                  },
                },
                maintainAspectRatio: false,
                scales: {
                  x: {
                    border: {
                      display: false,
                    },
                    grid: {
                      display: false,
                      drawBorder: false,
                    },
                    ticks: {
                      display: false,
                    },
                  },
                  y: {
                    min: 30,
                    max: 89,
                    display: false,
                    grid: {
                      display: false,
                    },
                    ticks: {
                      display: false,
                    },
                  },
                },
                elements: {
                  line: {
                    borderWidth: 1,
                    tension: 0.4,
                  },
                  point: {
                    radius: 4,
                    hitRadius: 10,
                    hoverRadius: 4,
                  },
                },
              }}
            />
          }
        /> */}
        </Link>
      </CCol>
      {isAdmin && (

      <CCol sm={6} xl={4} xxl={3}>
        <Link to={'/view-small-phones'} style={{ textDecoration: 'none' }}>
        <div className="dashboard-card">
          <div className="dashboard-card-title">Simu Ndogo</div>
          <div className="dashboard-card-value">
            {smallphoneCount}
          </div>
          <div className="d-flex align-items-center justify-content-between">
            <span className="text-gray-500" style={{ fontSize: '0.875rem' }}>
              Basic phones
            </span>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, var(--gray-500), var(--gray-600))',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              📞
            </div>
          </div>
        </div>
        </Link>
      </CCol>
      )}
      <CCol sm={6} xl={4} xxl={3}>
        <Link to={'/view-cash'} style={{ textDecoration: 'none' }}>
        <div className="dashboard-card">
          <div className="dashboard-card-title">Sales - Smart Phones</div>
          <div className="dashboard-card-value">
            {saleCount}
          </div>
          <div className="d-flex align-items-center justify-content-between">
            <span className="text-gray-500" style={{ fontSize: '0.875rem' }}>
              Total sales
            </span>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, var(--success-500), var(--success-600))',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              💰
            </div>
          </div>
        </div>
        </Link>
      </CCol>
      <CCol sm={6} xl={4} xxl={3}>
        <Link to={'/view-cash-small'}  style={{ textDecoration: 'none' }}>
        <div className="dashboard-card">
          <div className="dashboard-card-title">Sales - Simu Ndogo</div>
          <div className="dashboard-card-value">
            {SmallSaleCount}
          </div>
          <div className="d-flex align-items-center justify-content-between">
            <span className="text-gray-500" style={{ fontSize: '0.875rem' }}>
              Basic phone sales
            </span>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, var(--info-500), var(--info-600))',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              📊
            </div>
          </div>
        </div>
        </Link>
      </CCol>
      <CCol sm={6} xl={4} xxl={3}>
        <Link to={'/view-returns'} style={{ textDecoration: 'none' }}>
        <div className="dashboard-card">
          <div className="dashboard-card-title">Returns</div>
          <div className="dashboard-card-value">
            {returnCount}
          </div>
          <div className="d-flex align-items-center justify-content-between">
            <span className="text-gray-500" style={{ fontSize: '0.875rem' }}>
              Total returns
            </span>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, var(--warning-500), var(--warning-600))',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              ↩️
            </div>
          </div>
        </div>
        </Link>
      </CCol>
      {isAdmin && (

      <CCol sm={6} xl={4} xxl={3}>
        <Link to={'/view-users'} style={{ textDecoration: 'none' }}>
        <div className="dashboard-card">
          <div className="dashboard-card-title">Users</div>
          <div className="dashboard-card-value">
            {userCount}
          </div>
          <div className="d-flex align-items-center justify-content-between">
            <span className="text-gray-500" style={{ fontSize: '0.875rem' }}>
              System users
            </span>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, var(--error-500), var(--error-600))',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white'
            }}>
              👥
            </div>
          </div>
        </div>
        </Link>
      </CCol>
      )}
    </CRow>
  )
}

WidgetsDropdown.propTypes = {
  className: PropTypes.string,
  withCharts: PropTypes.bool,
}

export default WidgetsDropdown
