import React, { useState, useEffect } from 'react';
import {
  <PERSON>ard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CTable,
  CTableBody,
  CTableDataCell,
  CTableHead,
  CTableHeaderCell,
  CTableRow,
  CButton,
  CFormInput,
  CPagination,CFormLabel,
  CPaginationItem,CModal,CModalHeader,CModalTitle,
  CModalBody,CModalFooter
} from '@coreui/react';
import { Link } from 'react-router-dom';

import CIcon from '@coreui/icons-react';
import * as icon from '@coreui/icons';

import useFetch from '../../../hooks/useFetch'; 
import { api_path } from '../../../../environments';
import { toast } from 'react-toastify';
import axios from 'axios';
import { useAuth } from '../../../hooks/AuthContext'
// import { useLocation } from 'react-router-dom';

const CustomerPhone = () => {
  const { role } = useAuth()
  const isAdmin = role == 'admin';
//   const location = useLocation();
//   const queryParams = new URLSearchParams(location.search);
//   const groupID = queryParams.get('groupID');

//   const [visible,setVisible] = useState('')
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5; 

  const { data: allPhone , loading , error} = useFetch('other/allCPhone');

  const initialCashRecords = Array.isArray(allPhone)
  ? allPhone.map(phone => ({
      id: phone.PhoneID,
      pName: phone.PhoneName,
      imei1: phone.IMEI1,
      imei2: phone.IMEI2,
      price: phone.PhonePrice,
    //   quantity: phone.Quantity,
  }))
  : [];


  const filteredStaffs = initialCashRecords.filter(staff =>
    staff.pName.toLowerCase().includes(searchTerm.toLowerCase()) 
  );

  const totalPages = Math.ceil(filteredStaffs.length / itemsPerPage);
  
  const indexOfLastStaff = currentPage * itemsPerPage;
  const indexOfFirstStaff = indexOfLastStaff - itemsPerPage;
  const currentStaffs = filteredStaffs.slice(indexOfFirstStaff, indexOfLastStaff);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };


  const handleDelete =async (PhoneID) => {
    const response = await axios.post(`${api_path}/api/other/deletePhone/${PhoneID}`);

    if(response.status == 200){
      toast.success("Phone Deleted successfully");
      window.location.reload();
    }
  }
//   const handlePlusStock =async (PhoneID) => {
//     // console.log(PhoneID,pQuantity)
//     const data = {
//         pQuantity
//     }
//     const response = await axios.post(`${api_path}/api/other/plusStock/${PhoneID}`,data);

//     if(response.status == 200){
//       toast.success("Stock Added successfully");
//       setVisible(false)
//       setPQuantity('')
//       window.location.reload();
//     }
//   }

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading users: {error.message}</div>;

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="mb-4">
          <CCardHeader>
            <strong>All Customer Phone </strong>
            <div className='d-flex gap-2'>
              <CFormInput
                type="text"
                placeholder="Search by phone name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mt-2"
              />
            </div>
          </CCardHeader>
          <CCardBody>
            <CTable responsive>
              <CTableHead>
                <CTableRow>
                    <CTableHeaderCell scope="col">#</CTableHeaderCell>
                    <CTableHeaderCell scope="col">Phone</CTableHeaderCell>
                    <CTableHeaderCell scope="col">IMEI1</CTableHeaderCell>
                    <CTableHeaderCell scope="col">IMEI2</CTableHeaderCell>
                    {/* <CTableHeaderCell scope="col">quantity</CTableHeaderCell> */}
                    <CTableHeaderCell scope="col">Action</CTableHeaderCell>
                </CTableRow>
              </CTableHead>
              <CTableBody>
                {currentStaffs.map((phone,index) => (
                  <CTableRow key={phone.id}>
                    <CTableHeaderCell scope="row">{index + 1}</CTableHeaderCell>
                    <CTableDataCell>{phone.pName}</CTableDataCell>
                    <CTableDataCell>{phone.imei1}</CTableDataCell>
                    <CTableDataCell>{phone.imei2}</CTableDataCell>
                    {/* <CTableDataCell>{phone.quantity}</CTableDataCell> */}

                    {isAdmin && (
                      <>
                      <CTableDataCell className='d-flex gap-2'>
                        <Link to={'/add-phone?cPhoneID=' + phone.id}>
                        <CButton color="primary">
                          <CIcon icon={icon.cilArrowCircleBottom} />
                        </CButton>
                        </Link>
                        {/* <CButton 
                            color="danger" 
                            className='text-white' 
                            onClick={() => {
                              const isConfirmed = window.confirm("Are you sure you want to delete this record?");
                              
                              if (isConfirmed) {
                                handleDelete(phone.id);
                              }
                            }}

                          >                        
                          <CIcon icon={icon.cilTrash} />
                        </CButton> */}
                      </CTableDataCell>
                    </>
                    )}
                  </CTableRow>
                ))}
              </CTableBody>
            </CTable>
            <CPagination aria-label="Page navigation example" align="end">
              <CPaginationItem
                aria-label="Previous"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <span aria-hidden="true">&laquo;</span>
              </CPaginationItem>
              {Array.from({ length: totalPages }, (_, index) => (
                <CPaginationItem
                  key={index + 1}
                  active={index + 1 === currentPage}
                  onClick={() => handlePageChange(index + 1)}
                >
                  {index + 1}
                </CPaginationItem>
              ))}
              <CPaginationItem
                aria-label="Next"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <span aria-hidden="true">&raquo;</span>
              </CPaginationItem>
            </CPagination>
          </CCardBody>
        </CCard>
      </CCol>

        {/* <CModal backdrop="static" visible={visible} onClose={() => setVisible(false)}>
        <CModalHeader>
        <CModalTitle>Add In Stock</CModalTitle>
        </CModalHeader>
        <CModalBody>
            <CRow className="mb-3">
               {error && <p className="text-danger">{error}</p>} 
              <CFormLabel htmlFor="name" className="col-sm-2 col-form-label">
                Idadi:
              </CFormLabel>
              <div className="col-sm-10">
                <CFormInput
                  type="number"
                  id="name"
                //   value={pQuantity}
                //   onChange={(e) => setPQuantity(e.target.value)}
                />
              </div>
            </CRow>
        </CModalBody>
        <CModalFooter>
        <CButton color="secondary" onClick={() => setVisible(false)}>
            Close
        </CButton>
        <CButton color="primary" onClick={() => handlePlusStock(PhoneID)}>Save changes</CButton>
        </CModalFooter>
        </CModal> */}
    </CRow>

  );
};

export default CustomerPhone;
