import React from 'react'
import CIcon from '@coreui/icons-react'
import {
  cilLoop,
  cilCog,
  cilContact,
  cilCasino,
  cilAudioSpectrum,
  cilGolf,
  cilLanguage,
  cilDevices
} from '@coreui/icons'
import { CNavGroup, CNavItem } from '@coreui/react'


const _nav = [
  {
    component: CNavGroup,
    name: 'Cash',
    icon: <CIcon icon={cilGolf} customClassName="nav-icon" />,
    for: 'all',  
    items: [
      {
        component: CNavItem,
        name: 'Smart Phones',
        to: '/view-cash',
      },
      {
        component: CNavItem,
        name: '<PERSON>mu Ndog<PERSON>',
        to: '/view-cash-small',
      },
    ]
  },
  {
    component: CNavGroup,
    name: 'Top Up',
    icon: <CIcon icon={cilCasino} customClassName="nav-icon" />,
    for: 'all', 
    items: [
      {
        component: CNavItem,
        name: 'View',
        to: '/view-topups',
      },
      {
        component: CNavItem,
        name: 'Add',
        to: '/add-topup',
      },
    ]
  },
  {
    component: CNavGroup,
    name: 'Return',
    icon: <CIcon icon={cilLoop} customClassName="nav-icon" />,
    for: 'all', 
    items: [
      {
        component: CNavItem,
        name: 'View',
        to: '/view-returns',
      },
      {
        component: CNavItem,
        name: 'Add',
        to: '/add-return',
      },
    ]
  },
  {
    component: CNavGroup,
    name: 'Slow Payment',
    icon: <CIcon icon={cilAudioSpectrum} customClassName="nav-icon" />,
    for: 'all',  
    items: [
      {
        component: CNavItem,
        name: 'View',
        to: '/view-slow-payments',
      },
      {
        component: CNavItem,
        name: 'Add',
        to: '/add-slow-payment',
      },
    ]
  },
  {
    component: CNavGroup,
    name: 'Phones',
    icon: <CIcon icon={cilDevices} customClassName="nav-icon" />,
    for: 'admin', 
    items: [
      {
        component: CNavItem,
        name: 'Customer Phone',
        to: '/customer-phone',
      },
      {
        component: CNavItem,
        name: 'View Phones',
        to: '/view-phones',
      },
      {
        component: CNavItem,
        name: 'Add Phone',
        to: '/add-phone',
      },
      {
        component: CNavItem,
        name: 'Simu Ndogo',
        to: '/view-small-phones',
      },
    ]
  },
  {
    component: CNavGroup,
    name: 'Users',
    icon: <CIcon icon={cilContact} customClassName="nav-icon" />,
    for: 'admin', 
    items: [
      {
        component: CNavItem,
        name: 'View Users',
        to: '/view-users',
      },
      {
        component: CNavItem,
        name: 'Add User',
        to: '/add-users',
      },
    ]
  },
  {
    component: CNavGroup,
    name: 'Other',
    icon: <CIcon icon={cilLanguage} customClassName="nav-icon" />,
    for: 'all',
    items: [
      {
        component: CNavItem,
        name: 'Add a comment',
        to: '/add-comment',
      },
      {
        component: CNavItem,
        name: 'My Phones',
        to: '/report',
      },
    ],
  },
  // {
  //   component: CNavGroup,
  //   name: 'Settings',
  //   icon: <CIcon icon={cilCog} customClassName="nav-icon" />,
  //   for: 'all',
  //   items: [
  //     {
  //       component: CNavItem,
  //       name: 'General Setting',
  //       to: '/login',
  //     },
  //   ],
  // },
]

export default _nav
